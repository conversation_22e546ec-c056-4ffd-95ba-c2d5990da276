#!/usr/bin/env python3
"""
GTTC 报告同步调度器
支持立即执行和整点定时执行
"""

import time
import subprocess
import sys
from datetime import datetime, timedelta
import os

# 配置项
EXECUTE_ON_HOUR = True  # True: 整点执行 (如 14:00, 15:00), False: 每隔1小时执行
IMMEDIATE_START = True  # 容器启动时是否立即执行一次

def log_with_flush(message):
    """打印日志并立即刷新输出缓冲区"""
    print(message)
    sys.stdout.flush()

def run_sync():
    """执行同步任务"""
    try:
        log_with_flush(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 🚀 开始执行 GTTC 报告同步...")
        
        # 执行主同步脚本，不捕获输出，让它直接显示
        result = subprocess.run([
            sys.executable, 'gttc_to_feishu_uploader.py'
        ], cwd='/app', env=os.environ.copy())
        
        if result.returncode == 0:
            log_with_flush(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ✅ 同步任务完成成功")
        else:
            log_with_flush(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ❌ 同步任务失败，返回码: {result.returncode}")
                
    except Exception as e:
        log_with_flush(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ❌ 执行同步任务时发生异常: {e}")

def calculate_next_execution_time():
    """计算下次执行时间"""
    if EXECUTE_ON_HOUR:
        # 整点执行：计算到下个整点的秒数
        now = datetime.now()
        next_hour = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
        return (next_hour - now).total_seconds()
    else:
        # 每隔1小时执行
        return 3600

def main():
    """主调度循环"""
    log_with_flush("=" * 50)
    log_with_flush("🚀 GTTC 报告同步调度器启动")
    log_with_flush(f"⏰ 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if EXECUTE_ON_HOUR:
        log_with_flush("📅 模式: 整点执行 (每小时的00分)")
    else:
        log_with_flush("📅 模式: 间隔执行 (每隔1小时)")
    
    log_with_flush("=" * 50)
    
    # 是否立即执行
    if IMMEDIATE_START:
        log_with_flush("⚡ 配置为启动时立即执行...")
        run_sync()
    else:
        log_with_flush("⏳ 配置为等待下次计划时间执行...")
    
    # 主循环
    while True:
        try:
            # 计算等待时间
            wait_seconds = calculate_next_execution_time()
            
            if EXECUTE_ON_HOUR:
                next_time = datetime.now() + timedelta(seconds=wait_seconds)
                log_with_flush(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ⏰ 等待到下个整点执行... (下次: {next_time.strftime('%H:%M')})")
            else:
                log_with_flush(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ⏰ 等待下次执行... (1小时后)")
            
            # 分段等待，每10分钟显示一次剩余时间
            remaining_seconds = wait_seconds
            while remaining_seconds > 60:
                time.sleep(60)  # 等待1分钟
                remaining_seconds -= 60
                remaining_minutes = int(remaining_seconds // 60)
                if remaining_minutes > 0 and remaining_minutes % 10 == 0:  # 每10分钟显示一次
                    log_with_flush(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ⏳ 还有 {remaining_minutes} 分钟到下次执行...")
            
            # 等待剩余时间
            if remaining_seconds > 0:
                time.sleep(remaining_seconds)
            
            # 执行同步任务
            run_sync()
            
        except KeyboardInterrupt:
            log_with_flush(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 🛑 收到停止信号，正在退出...")
            break
        except Exception as e:
            log_with_flush(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ❌ 调度器发生异常: {e}")
            log_with_flush("⏳ 将在5分钟后重试...")
            time.sleep(300)  # 等待5分钟后重试

if __name__ == "__main__":
    main() 