#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CookieCloud 取数测试脚本
用于验证从CookieCloud服务端获取cookie信息的可行性
"""

import requests
import json
import hashlib
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import base64

class CookieCloudClient:
    def __init__(self, server_url, uuid, password):
        """
        初始化CookieCloud客户端
        
        Args:
            server_url: CookieCloud服务器地址
            uuid: 用户KEY·UUID
            password: 端对端加密密码
        """
        self.server_url = server_url.rstrip('/')
        self.uuid = uuid
        self.password = password
        self.session = requests.Session()
        
    def get_the_key(self):
        """获取加密密钥"""
        try:
            # 根据CookieCloud官方实现，密钥是UUID和密码的组合的MD5值
            key_string = self.uuid + '-' + self.password
            key = hashlib.md5(key_string.encode()).hexdigest()[:16]
            return key.encode()
            
        except Exception as e:
            print(f"❌ 生成密钥失败: {e}")
            return None
    
    def get_encrypted_data(self):
        """获取加密数据"""
        try:
            url = f"{self.server_url}/get/{self.uuid}"
            print(f"🔗 请求URL: {url}")
            
            response = self.session.get(url, timeout=10)
            print(f"📊 响应状态码: {response.status_code}")
            
            response.raise_for_status()
            
            data = response.json()
            print(f"📦 响应数据键: {list(data.keys())}")
            
            return data.get('encrypted')
            
        except Exception as e:
            print(f"❌ 获取加密数据失败: {e}")
            return None
    
    def decrypt_openssl_format(self, encrypted_data, password):
        """解密OpenSSL格式的数据"""
        try:
            print(f"🔐 尝试OpenSSL格式解密...")
            
            # Base64解码
            encrypted_bytes = base64.b64decode(encrypted_data)
            
            # 检查是否是OpenSSL格式 (以"Salted__"开头)
            if encrypted_bytes[:8] != b'Salted__':
                print("❌ 不是OpenSSL格式的数据")
                return None
            
            # 提取盐值
            salt = encrypted_bytes[8:16]
            ciphertext = encrypted_bytes[16:]
            
            print(f"🧂 盐值长度: {len(salt)} 字节")
            print(f"🔒 密文长度: {len(ciphertext)} 字节")
            
            # 使用EVP_BytesToKey算法生成密钥和IV
            key, iv = self.evp_bytes_to_key(password.encode(), salt, 16, 16)
            
            print(f"🔑 生成的密钥长度: {len(key)} 字节")
            print(f"🔐 生成的IV长度: {len(iv)} 字节")
            
            # AES解密
            cipher = AES.new(key, AES.MODE_CBC, iv)
            decrypted = cipher.decrypt(ciphertext)
            
            # 去除填充
            decrypted = unpad(decrypted, AES.block_size)
            
            # 解析JSON
            decrypted_str = decrypted.decode('utf-8')
            print(f"✅ 解密后数据长度: {len(decrypted_str)} 字符")
            
            return json.loads(decrypted_str)
            
        except Exception as e:
            print(f"❌ OpenSSL格式解密失败: {e}")
            return None
    
    def evp_bytes_to_key(self, password, salt, key_len, iv_len):
        """
        实现OpenSSL的EVP_BytesToKey算法
        """
        d = d_i = b''
        while len(d) < (key_len + iv_len):
            d_i = hashlib.md5(d_i + password + salt).digest()
            d += d_i
        return d[:key_len], d[key_len:key_len+iv_len]
    
    def decrypt_data(self, encrypted_data, key):
        """解密数据 - 尝试多种格式"""
        try:
            print(f"🔑 使用密钥长度: {len(key)} 字节")
            print(f"📝 加密数据长度: {len(encrypted_data)} 字符")
            
            # 首先尝试OpenSSL格式解密
            if encrypted_data.startswith('U2FsdGVkX1'):  # "Salted__" 的Base64编码开头
                print("🔍 检测到OpenSSL格式，尝试OpenSSL解密...")
                # 使用原始密码而不是处理后的密钥
                password = self.uuid + '-' + self.password
                result = self.decrypt_openssl_format(encrypted_data, password)
                if result:
                    return result
            
            # 如果OpenSSL格式失败，尝试标准AES-CBC
            print("🔍 尝试标准AES-CBC解密...")
            
            # Base64解码
            encrypted_bytes = base64.b64decode(encrypted_data)
            print(f"🔢 解码后数据长度: {len(encrypted_bytes)} 字节")
            
            # 检查数据长度是否合理
            if len(encrypted_bytes) < 16:
                print("❌ 加密数据太短，无法包含IV")
                return None
            
            # AES解密 (CBC模式)
            iv = encrypted_bytes[:16]  # 前16字节是IV
            ciphertext = encrypted_bytes[16:]
            
            print(f"🔐 IV长度: {len(iv)} 字节")
            print(f"🔒 密文长度: {len(ciphertext)} 字节")
            
            cipher = AES.new(key, AES.MODE_CBC, iv)
            decrypted = cipher.decrypt(ciphertext)
            
            # 尝试去除填充
            try:
                decrypted = unpad(decrypted, AES.block_size)
            except ValueError as e:
                print(f"⚠️ 标准去填充失败，尝试手动去填充: {e}")
                # 手动去除PKCS7填充
                padding_length = decrypted[-1]
                if padding_length <= AES.block_size:
                    decrypted = decrypted[:-padding_length]
                else:
                    print("❌ 填充长度异常")
                    return None
            
            # 解析JSON
            decrypted_str = decrypted.decode('utf-8')
            print(f"✅ 解密后数据长度: {len(decrypted_str)} 字符")
            
            return json.loads(decrypted_str)
            
        except Exception as e:
            print(f"❌ 解密失败: {e}")
            print(f"🔍 错误类型: {type(e).__name__}")
            return None
    
    def test_connection(self):
        """测试服务器连接"""
        try:
            url = f"{self.server_url}/get/{self.uuid}"
            response = self.session.get(url, timeout=10)
            print(f"🌐 服务器连接测试:")
            print(f"  状态码: {response.status_code}")
            print(f"  响应头: {dict(response.headers)}")
            print(f"  响应内容长度: {len(response.text)}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"  JSON数据键: {list(data.keys())}")
                    if 'encrypted' in data:
                        encrypted = data['encrypted']
                        print(f"  加密数据长度: {len(encrypted)}")
                        print(f"  加密数据前50字符: {encrypted[:50]}...")
                    return True
                except json.JSONDecodeError:
                    print(f"  响应内容: {response.text[:200]}...")
            
            return False
            
        except Exception as e:
            print(f"❌ 连接测试失败: {e}")
            return False
    
    def get_cookies(self):
        """获取并解密cookie数据"""
        print("🚀 开始获取CookieCloud数据...")
        
        # 先测试连接
        if not self.test_connection():
            print("❌ 服务器连接失败")
            return None
        
        # 获取密钥
        print("\n📝 生成解密密钥...")
        key = self.get_the_key()
        if not key:
            return None
        print("✅ 密钥生成成功")
        
        # 获取加密数据
        print("\n📡 获取服务器加密数据...")
        encrypted_data = self.get_encrypted_data()
        if not encrypted_data:
            return None
        print("✅ 加密数据获取成功")
        
        # 解密数据
        print("\n🔓 解密数据中...")
        decrypted_data = self.decrypt_data(encrypted_data, key)
        if not decrypted_data:
            return None
        print("✅ 数据解密成功")
        
        return decrypted_data
    
    def display_cookies(self, cookie_data):
        """显示cookie信息"""
        if not cookie_data:
            print("❌ 没有cookie数据可显示")
            return
        
        print("\n" + "="*60)
        print("🍪 CookieCloud 数据概览")
        print("="*60)
        
        # 显示基本信息
        if 'update_time' in cookie_data:
            print(f"📅 更新时间: {cookie_data['update_time']}")
        
        # 显示cookie统计
        total_cookies = 0
        domains = set()
        
        for domain, cookies in cookie_data.items():
            if domain in ['update_time']:
                continue
                
            if isinstance(cookies, list):
                total_cookies += len(cookies)
                domains.add(domain)
        
        print(f"🌐 域名数量: {len(domains)}")
        print(f"🍪 Cookie总数: {total_cookies}")
        
        # 显示各域名的cookie数量
        print("\n📊 各域名Cookie统计:")
        print("-" * 40)
        
        for domain, cookies in cookie_data.items():
            if domain in ['update_time']:
                continue
                
            if isinstance(cookies, list):
                print(f"  {domain}: {len(cookies)} 个cookie")
        
        # 显示部分cookie详情（前3个域名）
        print("\n🔍 Cookie详情预览 (前3个域名):")
        print("-" * 40)
        
        count = 0
        for domain, cookies in cookie_data.items():
            if domain in ['update_time'] or count >= 3:
                continue
                
            if isinstance(cookies, list) and cookies:
                print(f"\n🌐 {domain}:")
                for i, cookie in enumerate(cookies[:3]):  # 每个域名最多显示3个cookie
                    name = cookie.get('name', 'N/A')
                    value = cookie.get('value', 'N/A')
                    # 截断过长的值
                    if len(value) > 50:
                        value = value[:47] + "..."
                    print(f"  [{i+1}] {name} = {value}")
                
                if len(cookies) > 3:
                    print(f"  ... 还有 {len(cookies) - 3} 个cookie")
                count += 1

def main():
    """主函数"""
    print("🍪 CookieCloud 取数测试工具")
    print("=" * 50)
    
    # 从图片中读取的配置信息
    SERVER_URL = "https://cnarbguabrg.zeabur.app"
    UUID = "5csGiUGWFge13h6N3pvXYo"
    PASSWORD = "31KEAXegEv5Khth6oLZ8fW"
    
    print(f"🌐 服务器地址: {SERVER_URL}")
    print(f"🔑 UUID: {UUID}")
    print(f"🔐 密码: {PASSWORD[:10]}...")
    
    # 创建客户端
    client = CookieCloudClient(SERVER_URL, UUID, PASSWORD)
    
    # 获取cookie数据
    cookie_data = client.get_cookies()
    
    if cookie_data:
        # 显示cookie信息
        client.display_cookies(cookie_data)
        
        # 保存到文件
        output_file = "cookies_data.json"
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(cookie_data, f, ensure_ascii=False, indent=2)
            print(f"\n💾 数据已保存到: {output_file}")
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
        
        print("\n✅ 测试完成！CookieCloud服务可正常使用")
    else:
        print("\n❌ 测试失败！请检查配置信息和网络连接")

if __name__ == "__main__":
    main() 