import os
from dotenv import load_dotenv

# 启动时加载 .env 文件中的环境变量
load_dotenv()

# 飞书应用和多维表配置信息

# 多维表的Base ID (appToken)
APP_TOKEN = os.environ.get("FEISHU_APP_TOKEN")

# Table(数据表) ID
TABLE_ID = os.environ.get("FEISHU_TABLE_ID")

# 开放平台应用 App ID
APP_ID = os.environ.get("FEISHU_APP_ID")

# 开放平台应用 App Secret
APP_SECRET = os.environ.get("FEISHU_APP_SECRET")

# 飞书获取 tenant_access_token 的 URL
AUTH_URL = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/"

# 飞书上传文件的父类型，根据API文档，当前只支持 'bitable_file'
FEISHU_UPLOAD_PARENT_TYPE = "bitable_file"

# 飞书多维表字段名映射 (基于实际的表格结构)
# Key: gttc_scraper.py 中的字段名
# Value: 飞书多维表中的字段名
FIELD_MAPPING = {
    '序号': '序号',
    '报告编号': '报告编号',
    '受理时间': '受理时间',
    '委托单位': '委托单位',
    '样品名称': '样品名称',
    '款号': '款号',
    '颜色': '颜色',
    '商标': '商标',
    '面料编号': '面料编号',
    '生产单位': '生产单位',
    '缴费单位': '缴费单位',
    '检验项目': '检验项目',
    '检测进度': '检测进度',
    '报告结果': '报告结果',
    '不合格项目': '不合格项目',
    '预计发证时间': '预计发证时间',
    '出证时间': '出证时间',
    '金额': '金额',
    '费用交付状态': '费用交付状态',
    '受理单': '受理单',  # 文本字段
    '缴费通知单': '缴费通知单',  # 文本字段
    '报告书': '报告书',  # 文本字段
    '报告寄出信息': '报告寄出信息',
    '备注': '备注',
    '选中': '选中',
    
    # 附件字段映射 - 使用类型为15的附件字段
    '受理单下载链接': '受理单下载链接',  # 已存在的附件字段
    '缴费通知单下载链接': '缴费通知单下载链接',  # 已存在的附件字段
    '报告书下载链接': '报告书下载链接',  # 使用附件类型的字段而不是文本字段

} 