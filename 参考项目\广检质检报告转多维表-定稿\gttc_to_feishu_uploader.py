import json
import os
import time
import requests
from urllib.parse import urlparse, unquote, urljoin
import re
from datetime import datetime, timedelta
import concurrent.futures
import threading
import random
from requests.utils import requote_uri  # 新增导入

# 导入自定义模块
import feishu_config as config
import feishu_uploader_utils as feishu_utils
from gttc_scraper import GTTCReportScraper

# 临时附件下载目录
TEMP_DOWNLOAD_DIR = "temp_attachments"
if not os.path.exists(TEMP_DOWNLOAD_DIR):
    os.makedirs(TEMP_DOWNLOAD_DIR)

# 飞书API并发控制
FEISHU_UPLOAD_SEMAPHORE = threading.Semaphore(5)  # 限制同时上传的文件数量为5个
FEISHU_RATE_LIMIT_DELAY = 0.2  # 每次上传后的基础延迟（秒）

def sanitize_filename(filename):
    """清理文件名，移除或替换无效字符"""
    filename = re.sub(r'[\\/:*?"<>|]', '_', filename)
    filename = filename.replace('\n', ' ').replace('\r', ' ')
    return filename.strip()

def upload_attachment(file_path, app_token, access_token, report_id="", max_retries=3):
    """上传素材接口 - 包含并发控制和重试逻辑"""
    url = "https://open.feishu.cn/open-apis/drive/v1/medias/upload_all"
    headers = {
        "Authorization": f"Bearer {access_token}"
    }

    file_name = os.path.basename(file_path)
    file_size = os.path.getsize(file_path)

    form_data = {
        'file_name': file_name,
        'parent_type': 'bitable_file', # 多维表格附件类型
        'parent_node': app_token,      # 多维表格的 app_token
        'size': str(file_size),        # 文件大小，字符串形式
    }

    # 使用信号量控制并发
    with FEISHU_UPLOAD_SEMAPHORE:
        for attempt in range(max_retries):
            try:
                with open(file_path, 'rb') as f:
                    files = {'file': (file_name, f)} # 文件内容
                    if attempt > 0:  # 只在重试时显示尝试次数
                        print(f"[报告 {report_id}] 重试上传 (尝试 {attempt + 1}/{max_retries}): {file_name}")
                    response = requests.post(url, headers=headers, data=form_data, files=files, timeout=120)

                # 检查是否遇到速率限制
                if response.status_code == 429:
                    retry_after = int(response.headers.get('Retry-After', 60))
                    print(f"[报告 {report_id}] 遇到速率限制，等待 {retry_after} 秒后重试...")
                    time.sleep(retry_after)
                    continue

                if response.status_code == 200:
                    response_json = response.json()
                    if response_json.get("code") == 0:
                        file_token = response_json.get("data", {}).get("file_token")
                        if file_token:
                            # 成功后添加延迟以避免过快请求
                            delay = FEISHU_RATE_LIMIT_DELAY + random.uniform(0, 0.1)
                            time.sleep(delay)
                            return file_token
                        else:
                            print(f"[报告 {report_id}] 上传成功但未在响应中找到 file_token: {response.text}")
                            return None
                    elif response_json.get("code") == 99991400:  # 飞书常见的速率限制错误码
                        print(f"[报告 {report_id}] 飞书API速率限制，等待后重试... (尝试 {attempt + 1}/{max_retries})")
                        delay = (2 ** attempt) + random.uniform(0, 1)  # 指数退避
                        time.sleep(delay)
                        continue
                    else:
                        print(f"[报告 {report_id}] 飞书 API 错误: code={response_json.get('code')}, msg={response_json.get('msg')}")
                        if attempt == max_retries - 1:  # 最后一次尝试
                            return None
                        delay = (2 ** attempt) + random.uniform(0, 1)
                        time.sleep(delay)
                        continue
                else:
                    print(f"[报告 {report_id}] 上传失败: HTTP {response.status_code} - {response.text}")
                    if attempt == max_retries - 1:  # 最后一次尝试
                        return None
                    delay = (2 ** attempt) + random.uniform(0, 1)
                    time.sleep(delay)
                    continue

            except requests.exceptions.Timeout:
                print(f"[报告 {report_id}] 上传文件 {file_name} 超时 (尝试 {attempt + 1}/{max_retries})")
                if attempt == max_retries - 1:
                    return None
                time.sleep(2 ** attempt)
                continue
            except requests.exceptions.RequestException as e:
                print(f"[报告 {report_id}] 上传文件时发生 requests 错误: {e} (尝试 {attempt + 1}/{max_retries})")
                if attempt == max_retries - 1:
                    return None
                time.sleep(2 ** attempt)
                continue
            except Exception as e:
                print(f"[报告 {report_id}] 上传文件时发生未知错误: {e} (尝试 {attempt + 1}/{max_retries})")
                if attempt == max_retries - 1:
                    return None
                time.sleep(2 ** attempt)
                continue

        return None

def download_file(url, download_path, session, report_id=""):
    """下载文件到指定路径，使用报告ID确保文件名唯一"""
    if not url or not url.startswith(('http:', 'https:')):
        print(f"[报告 {report_id}] 无效的下载链接: {url}")
        return None
    try:
        # GTTC网站的链接可能是相对的，需要补全
        if not urlparse(url).netloc:
            url = urljoin(GTTCReportScraper().base_url, url)
        
        # 新增：URL规范化处理
        # 1. 收敛双斜杠为单斜杠（保留协议后的双斜杠）
        url = re.sub(r'(?<!:)/{2,}', '/', url)
        # 2. 对中文、空格等特殊字符进行百分号编码
        url = requote_uri(url)
        
        print(f"[报告 {report_id}] 尝试下载附件: {url}")
        
        # 新增：添加必要的请求头
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
            "Referer": "https://www.gttc.net.cn/",
        }
        
        response = session.get(url, headers=headers, stream=True, timeout=60, verify=False)
        response.raise_for_status()

        # 尝试从 Content-Disposition 获取文件名
        content_disposition = response.headers.get('content-disposition')
        original_filename = None
        if content_disposition:
            filenames = re.findall('filename=(.?)([^;\n]*)"*', content_disposition)
            if filenames:
                original_filename = unquote(filenames[0][1])

        if not original_filename:
            # 如果无法从header获取，则从URL路径中提取
            parsed_url = urlparse(url)
            original_filename = os.path.basename(parsed_url.path)
            if not original_filename:
                original_filename = f"downloaded_file_{int(time.time())}"
        # 新增：确保文件名是解码后的中文
        original_filename = unquote(original_filename)
        sanitized_original_filename = sanitize_filename(original_filename)

        # 使用报告ID和时间戳确保文件名唯一
        name_part, ext_part = os.path.splitext(sanitized_original_filename)
        timestamp = int(time.time() * 1000)  # 毫秒级时间戳
        unique_filename = f"{report_id}_{timestamp}_{name_part[:50]}{ext_part}"

        file_full_path = os.path.join(download_path, unique_filename)

        with open(file_full_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        print(f"[报告 {report_id}] 文件已下载: {file_full_path}")
        return file_full_path
    except requests.exceptions.RequestException as e:
        print(f"[报告 {report_id}] 下载文件失败 {url}: {e}")
        return None
    except Exception as e:
        print(f"[报告 {report_id}] 下载或保存文件时发生未知错误 {url}: {e}")
        return None

def process_single_report(report_data, scraper_session, access_token, item_idx, total_items, existing_feishu_attachment_fields=None):
    """处理单条报告数据，包括下载和上传附件
    
    Args:
        report_data (dict): 从GTTC抓取的单条报告数据。
        scraper_session (requests.Session): 用于下载的requests session。
        access_token (str): 飞书 tenant_access_token。
        item_idx (int): 当前处理记录的索引（用于日志）。
        total_items (int): 总记录数（用于日志）。
        existing_feishu_attachment_fields (dict, optional): 
            对于更新操作，传入已存在记录的附件字段当前值。
            键为飞书附件字段名，值为飞书返回的附件对象列表 (e.g., [{'file_token': ...}]).
            Defaults to None (表示是新增记录或不需要特殊处理附件的更新).
    """
    report_id = report_data.get('报告编号', 'N/A')
    # 仅当 item_idx 和 total_items 有意义时打印进度（例如，不是在单纯的字段比较阶段调用时）
    if item_idx > 0 and total_items > 0:
        print(f"\n[报告 {report_id}] 开始处理第 {item_idx}/{total_items} 条报告...")
    else:
        print(f"\n[报告 {report_id}] 准备处理字段...")

    feishu_record_fields = {}
    # ... (stats dictionary initialization remains the same)
    stats = {
        'report_downloaded': False,
        'detection_status': report_data.get('检测进度', '').strip(),
        'report_book_status': report_data.get('报告书', '').strip(),
        'report_truly_ready': False, 
        'data_inconsistent': False,  
        'report_pending': False      
    }

    detection_status = stats['detection_status']
    report_book_status = stats['report_book_status']

    if detection_status == '已出证' and report_book_status == '可下载':
        stats['report_truly_ready'] = True
    elif detection_status in ['已受理', '正在检测', '检测完成', '报告审核']:
        stats['report_pending'] = True

    # 1. 字段映射 (非附件字段)
    number_fields = {'金额'}  # 移除'序号'
    single_select_fields = {
        '委托单位', '商标', '面料编号', '生产单位', '缴费单位',
        '检测进度', '报告结果', '费用交付状态', '缴费通知单', '报告书'
    } 

    for scraper_key, feishu_key in config.FIELD_MAPPING.items():
        if feishu_key and scraper_key in report_data and not scraper_key.endswith('下载链接') and feishu_key != '序号':
            value = report_data[scraper_key]
            if feishu_key in number_fields and value is not None:
                if isinstance(value, str) and value.strip() == "": 
                    feishu_record_fields[feishu_key] = None
                elif isinstance(value, (int, float)):
                     feishu_record_fields[feishu_key] = value
                else: 
                    try:
                        clean_value = str(value).replace(',', '').strip()
                        num_value = float(clean_value)
                        if num_value.is_integer():
                            feishu_record_fields[feishu_key] = int(num_value)
                        else:
                            feishu_record_fields[feishu_key] = num_value
                    except ValueError:
                        print(f"[报告 {report_id}] 警告: 字段 '{scraper_key}' (映射到 '{feishu_key}') 的值 '{value}' 无法转换为数字，将置为空。")
                        feishu_record_fields[feishu_key] = None
            elif feishu_key in single_select_fields:
                if value is not None and str(value).strip():
                    feishu_record_fields[feishu_key] = str(value).strip()
                else:
                    feishu_record_fields[feishu_key] = None
            elif isinstance(value, (int, float)):
                feishu_record_fields[feishu_key] = value
            else: 
                feishu_record_fields[feishu_key] = str(value).strip() if value is not None else ""

    # 2. 处理三个附件字段
    attachment_mappings = {
        '受理单下载链接': config.FIELD_MAPPING.get('受理单下载链接'),
        '缴费通知单下载链接': config.FIELD_MAPPING.get('缴费通知单下载链接'),
        '报告书下载链接': config.FIELD_MAPPING.get('报告书下载链接')
    }

    for scraper_key, feishu_field_name in attachment_mappings.items():
        if not feishu_field_name: # 如果映射中不存在该附件字段，则跳过
            print(f"[报告 {report_id}] 警告: 附件字段 '{scraper_key}' 在 FIELD_MAPPING 中没有对应的飞书字段名，跳过处理。")
            continue

        feishu_record_fields[feishu_field_name] = [] # 默认设为空列表
        gttc_download_url = report_data.get(scraper_key)

        # 检查GTTC是否提供了有效的下载链接
        is_gttc_url_valid = (
            gttc_download_url and
            str(gttc_download_url).strip() and
            str(gttc_download_url).lower() != "不可用" and
            str(gttc_download_url).startswith(('http:', 'https:'))
        )

        # 获取飞书中已有的该附件信息 (如果是在更新模式下)
        existing_attachment_in_feishu = None
        if existing_feishu_attachment_fields:
            existing_attachment_in_feishu = existing_feishu_attachment_fields.get(feishu_field_name)

        if is_gttc_url_valid:
            # GTTC 提供了有效链接
            should_download_and_upload = True # 默认需要下载上传
            
            if existing_feishu_attachment_fields is not None: # 表明是更新操作
                if existing_attachment_in_feishu and isinstance(existing_attachment_in_feishu, list) and len(existing_attachment_in_feishu) > 0:
                    # 飞书中已存在此附件，则不重新下载上传，保留现有附件
                    print(f"[报告 {report_id}] 附件 '{feishu_field_name}' 在飞书中已存在，将保留现有附件。")
                    feishu_record_fields[feishu_field_name] = existing_attachment_in_feishu
                    should_download_and_upload = False 
                # else: 飞书中不存在此附件，即使是更新操作，也需要下载上传 (should_download_and_upload 保持 True)
            
            if should_download_and_upload:
                print(f"[报告 {report_id}] 附件 '{feishu_field_name}': GTTC链接有效，准备下载。")
                # (针对报告书下载链接的特殊逻辑，如多URL处理，可以放在这里或download_file内部)
                urls_to_try = [str(gttc_download_url).strip()] # 默认只有一个URL
                if scraper_key == '报告书下载链接': # 报告书链接可能有多行
                    urls_to_try = [url.strip() for url in str(gttc_download_url).split('\n') 
                                   if url.strip() and url.strip().startswith(('http:', 'https:'))]
                
                downloaded_file_path = None
                for single_url in urls_to_try:
                    # 特殊下载条件判断 (例如报告书是否已出证)
                    can_download_this_attachment = True
                    if scraper_key == '报告书下载链接':
                        if not stats['report_truly_ready']:
                             print(f"[报告 {report_id}] 报告书尚未真正就绪 (检测进度: {detection_status}, 报告书状态: {report_book_status})，跳过下载 {single_url}")
                             can_download_this_attachment = False
                             # 如果报告书未就绪，我们应该将飞书字段清空，而不是跳过上传导致保留旧附件
                             feishu_record_fields[feishu_field_name] = []
                             stats['data_inconsistent'] = True # 标记潜在的数据不一致

                    if not can_download_this_attachment:
                        continue # 跳过这个URL的下载

                    # 确保URL是绝对的 (虽然GTTC scraper应该已经处理了)
                    if not urlparse(single_url).scheme:
                        single_url = urljoin(GTTCReportScraper().base_url, single_url)
                    
                    downloaded_file_path = download_file(single_url, TEMP_DOWNLOAD_DIR, scraper_session, report_id)
                    if downloaded_file_path:
                        if scraper_key == '报告书下载链接': stats['report_downloaded'] = True
                        break # 只要有一个URL下载成功就停止
                
                if downloaded_file_path:
                    print(f"[报告 {report_id}] 准备上传 {os.path.basename(downloaded_file_path)} ({feishu_field_name}) 到飞书...")
                    file_token = upload_attachment(downloaded_file_path, config.APP_TOKEN, access_token, report_id)
                    if file_token:
                        feishu_record_fields[feishu_field_name] = [{"file_token": file_token}]
                        print(f"[报告 {report_id}] 成功上传附件 '{feishu_field_name}' 并获取 file_token: {file_token}")
                    else:
                        print(f"[报告 {report_id}] 上传附件 {os.path.basename(downloaded_file_path)} ({feishu_field_name}) 失败。飞书字段将为空。")
                        feishu_record_fields[feishu_field_name] = [] # 上传失败则清空
                    try:
                        os.remove(downloaded_file_path)
                        # print(f"[报告 {report_id}] 已删除临时文件: {os.path.basename(downloaded_file_path)}")
                    except OSError as e:
                        print(f"[报告 {report_id}] 删除临时文件 {os.path.basename(downloaded_file_path)} 失败: {e}")
                else:
                    # 如果所有URL都下载失败 (对于报告书，且它已就绪)
                    if not (scraper_key == '报告书下载链接' and not stats['report_truly_ready']):
                        print(f"[报告 {report_id}] 下载附件 '{scraper_key}' ({gttc_download_url}) 失败，飞书字段 '{feishu_field_name}' 将为空。")
                    feishu_record_fields[feishu_field_name] = [] # 下载失败则清空
        else:
            # GTTC 链接无效或不可用
            print(f"[报告 {report_id}] 附件 '{feishu_field_name}': GTTC链接无效或不可用 ('{gttc_download_url}')。飞书字段将为空。")
            feishu_record_fields[feishu_field_name] = [] # GTTC链接无效，清空飞书字段
            if scraper_key == '报告书下载链接' and stats['report_truly_ready']:
                # 如果报告书已出证但链接无效，标记为数据不一致
                stats['data_inconsistent'] = True

    return {"fields": feishu_record_fields, "stats": stats}

def is_equivalent(val1, val2):
    # 空字符串和None视为等价
    return (val1 is None or str(val1).strip() == '') and (val2 is None or str(val2).strip() == '') or str(val1).strip() == str(val2).strip()

def process_and_write(report_item_from_gttc, i, total, existing_feishu_records_map, fields_for_direct_comparison, attachment_mappings, scraper, access_token):
    gttc_report_id = report_item_from_gttc.get('报告编号', 'N/A')
    print(f"\n正在检查GTTC报告: {gttc_report_id} ({i+1}/{total})...", end=" ")
    result = {'skipped': False, 'stats': None}
    if gttc_report_id in existing_feishu_records_map:
        print("已存在于飞书，检查是否更新...")
        existing_record_in_feishu = existing_feishu_records_map[gttc_report_id]
        existing_fields_in_feishu = existing_record_in_feishu['fields']
        feishu_record_id_to_update = existing_record_in_feishu['record_id']

        needs_metadata_update = False
        for scraper_key, feishu_key_from_mapping in config.FIELD_MAPPING.items():
            if feishu_key_from_mapping in fields_for_direct_comparison:
                gttc_value = report_item_from_gttc.get(scraper_key, '')
                feishu_value = existing_fields_in_feishu.get(feishu_key_from_mapping, '')
                if not is_equivalent(gttc_value, feishu_value):
                    print(f"  字段 '{feishu_key_from_mapping}' 变化: 飞书='{feishu_value}', GTTC='{gttc_value}'")
                    needs_metadata_update = True
                    break

        attachments_need_action = False
        current_feishu_attachments_for_this_record = {}
        for scraper_att_key, feishu_att_key in attachment_mappings.items():
            if not feishu_att_key: continue
            current_feishu_attachments_for_this_record[feishu_att_key] = existing_fields_in_feishu.get(feishu_att_key)
            gttc_att_url = str(report_item_from_gttc.get(scraper_att_key, '')).strip()
            is_gttc_url_valid = gttc_att_url and gttc_att_url.lower() != "不可用" and gttc_att_url.startswith(('http:','https:'))
            feishu_has_this_attachment = current_feishu_attachments_for_this_record[feishu_att_key] and \
                                       isinstance(current_feishu_attachments_for_this_record[feishu_att_key], list) and \
                                       len(current_feishu_attachments_for_this_record[feishu_att_key]) > 0
            if is_gttc_url_valid and not feishu_has_this_attachment:
                print(f"  发现新附件需添加: '{feishu_att_key}' (GTTC链接: {gttc_att_url[:50]}...)")
                attachments_need_action = True
            elif not is_gttc_url_valid and feishu_has_this_attachment:
                print(f"  附件需移除: '{feishu_att_key}' (GTTC链接失效或不可用)")
                attachments_need_action = True

        if needs_metadata_update or attachments_need_action:
            print(f"  报告 {gttc_report_id} 需要更新。正在处理...")
            processed_data = process_single_report(
                report_item_from_gttc,
                scraper.session,
                access_token,
                0, 0,
                existing_feishu_attachment_fields=current_feishu_attachments_for_this_record
            )
            if processed_data and processed_data.get("fields"):
                update_result, update_count, update_failed = feishu_utils.batch_update_records(
                    config.APP_TOKEN, config.TABLE_ID,
                    [{"record_id": feishu_record_id_to_update, "fields": processed_data["fields"]}],
                    access_token
                )
                if update_result:
                    print(f"  成功更新记录: {gttc_report_id}")
                else:
                    print(f"  更新记录失败: {gttc_report_id}，详情: {update_failed}")
                if "stats" in processed_data: result['stats'] = processed_data["stats"]
            else:
                print(f"警告: 报告 (编号: {gttc_report_id}) 更新处理后返回空或无效结果，将跳过此条记录的更新。")
        else:
            print("  无元数据变化且无新附件添加/旧附件移除，跳过更新。")
            result['skipped'] = True
    else:
        print("为新记录，准备添加...")
        processed_data = process_single_report(
            report_item_from_gttc,
            scraper.session,
            access_token,
            i + 1, total
        )
        if processed_data and processed_data.get("fields"):
            add_result, add_count, add_failed = feishu_utils.batch_add_records(
                config.APP_TOKEN, config.TABLE_ID,
                [{"fields": processed_data["fields"]}],
                access_token
            )
            if add_result:
                print(f"  成功添加新记录: {gttc_report_id}")
            else:
                print(f"  添加新记录失败: {gttc_report_id}，详情: {add_failed}")
            if "stats" in processed_data: result['stats'] = processed_data["stats"]
        else:
            print(f"警告: 报告 (编号: {gttc_report_id}) 添加处理后返回空或无效结果，将跳过此条记录的添加。")
    return result

def main():
    print("开始执行GTTC报告数据到飞书多维表的上传流程...")

    print("获取飞书access_token...")
    access_token = feishu_utils.get_tenant_access_token()
    if not access_token:
        print("无法获取飞书access_token，程序终止。")
        return

    print("\n步骤 1: 从GTTC抓取报告数据...")
    scraper = GTTCReportScraper()
    today = datetime.now()
    start_date = today - timedelta(days=30)
    end_date = today
    start_date_str = start_date.strftime("%Y-%m-%d")
    end_date_str = end_date.strftime("%Y-%m-%d")
    print(f"正在获取日期范围 {start_date_str} 到 {end_date_str} 的数据（最近3天）...")
    date_params = {
        "AccceptTimeFrom": start_date_str,
        "AccceptTimeTo": end_date_str
    }
    response = scraper.make_request(params=date_params)
    if not response:
        print("从GTTC获取数据失败，程序终止。")
        return
    gttc_data = scraper.parse_html_table(response.text)
    if not gttc_data:
        print("未从GTTC解析到任何数据，程序终止。")
        return
    print(f"成功从GTTC抓取并解析到 {len(gttc_data)} 条报告数据。")

    if gttc_data:
        print("\n第一条抓取到的记录预览 (部分):")
        for key, value in list(gttc_data[0].items())[:7]:
            print(f"  {key}: {value}")
        if len(gttc_data[0]) > 7: print(f"  ... (还有 {len(gttc_data[0]) - 7} 个字段)")

    print(f"\n步骤 2: 增量更新模式 - 获取飞书现有记录并分类...")
    all_feishu_field_names_in_mapping = list(set(config.FIELD_MAPPING.values()))
    existing_feishu_records_map = feishu_utils.get_bitable_records(
        config.APP_TOKEN, config.TABLE_ID, access_token, field_names=all_feishu_field_names_in_mapping
    )

    records_to_add = []
    records_to_update = []
    skipped_count = 0
    all_processed_stats = []

    # Define attachment mappings (scraper key to Feishu field name)
    # This should be consistent with how process_single_report interprets them
    attachment_mappings = {
        '受理单下载链接': config.FIELD_MAPPING.get('受理单下载链接'),
        '缴费通知单下载链接': config.FIELD_MAPPING.get('缴费通知单下载链接'),
        '报告书下载链接': config.FIELD_MAPPING.get('报告书下载链接')
    }

    # Identify Feishu attachment field names (actual names in Feishu table)
    feishu_actual_attachment_field_names = [
        name for name in attachment_mappings.values() if name # Filter out None if a mapping is missing
    ]
    
    # Fields for direct textual/numerical comparison (non-attachment fields)
    fields_for_direct_comparison = [
        f_name for f_name in all_feishu_field_names_in_mapping 
        if f_name not in feishu_actual_attachment_field_names and f_name != '序号'
    ]

    total = len(gttc_data)
    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
        futures = [
            executor.submit(
                process_and_write,
                report_item_from_gttc, i, total,
                existing_feishu_records_map, fields_for_direct_comparison, attachment_mappings,
                scraper, access_token
            )
            for i, report_item_from_gttc in enumerate(gttc_data)
        ]
        for future in concurrent.futures.as_completed(futures):
            res = future.result()
            if res.get('skipped'):
                skipped_count += 1
            if res.get('stats'):
                all_processed_stats.append(res['stats'])
    print(f"\n处理完成，跳过 {skipped_count} 条无变化的记录。")

    if all_processed_stats:
        total_processed_count = len(all_processed_stats)
        truly_ready_count = sum(1 for stat in all_processed_stats if stat.get('report_truly_ready'))
        downloaded_count = sum(1 for stat in all_processed_stats if stat.get('report_downloaded'))
        data_inconsistent_count = sum(1 for stat in all_processed_stats if stat.get('data_inconsistent'))
        pending_count = sum(1 for stat in all_processed_stats if stat.get('report_pending'))
        
        print(f"\n📊 报告书处理统计 (基于 {total_processed_count} 条实际处理的报告):")
        print(f"  🎯 真正可下载 (已出证且可下载): {truly_ready_count} 条")
        print(f"  📥 成功下载报告书: {downloaded_count} 条")
        print(f"  ⚠️  数据不一致 (如已出证但链接无效): {data_inconsistent_count} 条")
        print(f"  ⏳ 报告书处理中 (未出证等): {pending_count} 条")
        if truly_ready_count > 0:
            success_rate = (downloaded_count / truly_ready_count) * 100
            print(f"  📈 报告书下载成功率 (已就绪报告中): {downloaded_count}/{truly_ready_count} ({success_rate:.1f}%)")
        elif downloaded_count > 0 : # If no reports were "truly_ready" but some were downloaded (e.g. pending but link worked)
             print(f"  📈 报告书下载成功率: N/A (没有报告被标记为'真正可下载'，但下载了 {downloaded_count} 份)")


    # Cleanup TEMP_DOWNLOAD_DIR
    if os.path.exists(TEMP_DOWNLOAD_DIR):
        print(f"\n清理临时附件目录: {TEMP_DOWNLOAD_DIR}...")
        cleaned_count = 0
        error_count = 0
        for item in os.listdir(TEMP_DOWNLOAD_DIR):
            item_path = os.path.join(TEMP_DOWNLOAD_DIR, item)
            try:
                if os.path.isfile(item_path) or os.path.islink(item_path):
                    os.unlink(item_path)
                    cleaned_count +=1
                elif os.path.isdir(item_path): # Should not happen if only files are downloaded
                    shutil.rmtree(item_path) 
                    cleaned_count +=1
            except Exception as e:
                print(f"  删除 {item_path} 失败: {e}")
                error_count +=1
        
        if error_count == 0 and not os.listdir(TEMP_DOWNLOAD_DIR):
            try:
                os.rmdir(TEMP_DOWNLOAD_DIR)
                print(f"已成功清理并删除临时附件目录: {TEMP_DOWNLOAD_DIR}")
            except OSError as e:
                print(f"删除临时附件目录 {TEMP_DOWNLOAD_DIR} 本身失败: {e} (可能仍有内容或权限问题)。")
        elif error_count > 0:
             print(f"临时附件目录 {TEMP_DOWNLOAD_DIR} 清理完毕，{cleaned_count}项已删除，{error_count}项删除失败。")
        else:
             print(f"临时附件目录 {TEMP_DOWNLOAD_DIR} 已清理，{cleaned_count}项已删除，目录本身未删除，因其不为空或发生其他错误。")
    else:
        print(f"临时附件目录 {TEMP_DOWNLOAD_DIR} 未找到，无需清理。")

    print("\nGTTC报告数据到飞书多维表的上传流程结束。")

if __name__ == "__main__":
    main()