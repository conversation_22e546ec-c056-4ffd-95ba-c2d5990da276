#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Data validation utilities for OA to Feishu automation
"""

import re
from typing import Any, Dict, List, Optional
from datetime import datetime

def validate_email(email: str) -> bool:
    """
    Validate email format
    
    Args:
        email: Email string to validate
        
    Returns:
        True if valid email format
    """
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def validate_phone(phone: str) -> bool:
    """
    Validate phone number format
    
    Args:
        phone: Phone number string to validate
        
    Returns:
        True if valid phone format
    """
    # Remove common separators
    clean_phone = re.sub(r'[-\s\(\)]', '', phone)
    # Check if it's all digits and reasonable length
    return clean_phone.isdigit() and 7 <= len(clean_phone) <= 15

def validate_amount(amount: Any) -> bool:
    """
    Validate monetary amount
    
    Args:
        amount: Amount value to validate
        
    Returns:
        True if valid amount
    """
    try:
        float_amount = float(amount)
        return float_amount >= 0
    except (ValueError, TypeError):
        return False

def validate_date(date_str: str) -> bool:
    """
    Validate date string format
    
    Args:
        date_str: Date string to validate
        
    Returns:
        True if valid date format
    """
    if not date_str:
        return False
        
    date_formats = ['%Y-%m-%d', '%Y/%m/%d', '%m/%d/%Y', '%d/%m/%Y']
    
    for fmt in date_formats:
        try:
            datetime.strptime(date_str, fmt)
            return True
        except ValueError:
            continue
    
    return False

def clean_text(text: Any) -> str:
    """
    Clean and normalize text data
    
    Args:
        text: Text to clean
        
    Returns:
        Cleaned text string
    """
    if text is None:
        return ""
    
    text_str = str(text).strip()
    # Remove extra whitespace
    text_str = re.sub(r'\s+', ' ', text_str)
    return text_str

def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> List[str]:
    """
    Validate that required fields are present and not empty
    
    Args:
        data: Data dictionary to validate
        required_fields: List of required field names
        
    Returns:
        List of missing field names
    """
    missing_fields = []
    
    for field in required_fields:
        if field not in data or not data[field] or str(data[field]).strip() == "":
            missing_fields.append(field)
    
    return missing_fields

def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename for safe file operations
    
    Args:
        filename: Original filename
        
    Returns:
        Sanitized filename
    """
    # Remove or replace invalid characters
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # Remove leading/trailing dots and spaces
    sanitized = sanitized.strip('. ')
    # Limit length
    if len(sanitized) > 255:
        name, ext = sanitized.rsplit('.', 1) if '.' in sanitized else (sanitized, '')
        sanitized = name[:255-len(ext)-1] + ('.' + ext if ext else '')
    
    return sanitized or 'unnamed_file'
