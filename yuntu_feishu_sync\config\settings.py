#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云图数据同步到飞书多维表 - 配置文件
"""

import os
from dotenv import load_dotenv
from dataclasses import dataclass
from typing import Optional, Dict, Any

# 加载环境变量
load_dotenv()

@dataclass
class CookieCloudConfig:
    """CookieCloud配置"""
    server_url: str
    uuid: str
    password: str

@dataclass
class FeishuConfig:
    """飞书配置"""
    app_id: str
    app_secret: str
    base_id: str  # 多维表app_token
    table_id: str  # 表格ID
    auth_url: str = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/"
    base_url: str = "https://open.feishu.cn/open-apis/"

@dataclass
class YuntuConfig:
    """云图配置"""
    base_url: str = "https://yuntu.oceanengine.com"
    api_endpoint: str = "/measurement/api/eva/get_trigger_insight_effect_analysis"
    domain: str = "yuntu.oceanengine.com"
    timeout: int = 30
    max_retries: int = 3
    retry_interval: int = 2

@dataclass
class AppConfig:
    """应用配置"""
    log_level: str = "INFO"
    date_range_days: int = 1  # 默认查询1天的数据
    batch_size: int = 500  # 批量处理大小

class Settings:
    """配置管理器"""
    
    def __init__(self):
        self._validate_env_vars()
        
        self.cookiecloud = CookieCloudConfig(
            server_url=os.getenv('COOKIECLOUD_SERVER_URL', ''),
            uuid=os.getenv('COOKIECLOUD_UUID', ''),
            password=os.getenv('COOKIECLOUD_PASSWORD', '')
        )

        self.feishu = FeishuConfig(
            app_id=os.getenv('FEISHU_APP_ID', ''),
            app_secret=os.getenv('FEISHU_APP_SECRET', ''),
            base_id=os.getenv('FEISHU_BASE_ID', 'LLsAbYzcaaRF6ws6e05c74STnee'),  # 默认值
            table_id=os.getenv('FEISHU_TABLE_ID', 'tblrpauvighksPhI')  # 默认值
        )
        
        self.yuntu = YuntuConfig()
        
        self.app = AppConfig(
            log_level=os.getenv('LOG_LEVEL', 'INFO'),
            date_range_days=int(os.getenv('DATE_RANGE_DAYS', '1')),
            batch_size=int(os.getenv('BATCH_SIZE', '500'))
        )
    
    def _validate_env_vars(self):
        """验证必需的环境变量"""
        required_vars = [
            'COOKIECLOUD_SERVER_URL',
            'COOKIECLOUD_UUID', 
            'COOKIECLOUD_PASSWORD',
            'FEISHU_APP_ID',
            'FEISHU_APP_SECRET'
        ]
        
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        if missing_vars:
            raise ValueError(f"缺少必需的环境变量: {', '.join(missing_vars)}")

# 全局设置实例
settings = Settings()

# 云图API请求参数模板
YUNTU_REQUEST_PARAMS = {
    "level_1_trigger_point_id": "0",
    "level_2_trigger_point_id": "0", 
    "start_date": "",  # 格式: 2025-03-31
    "end_date": "",    # 格式: 2025-03-31
    "industry_id": "29",
    "brand_id": "11452881",
    "date_type": 1,
    "benchmark_type": 2,
    "subsequent_trigger_point_level": 3,
    "need_level_2_aggred": False,
    "stage": 3,
    "search_window": 1,
    "convert_model": 5,
    "convert_window": 3
}

# 字段映射配置 - 云图字段到飞书字段的映射
# 基于实际表格结构更新映射，记录ID是自动生成的，不需要填写
FIELD_MAPPING = {
    # 触点信息 (记录ID是自动生成的，不在映射中)
    'trigger_point_id': '触点ID',  # 触点ID (如果表格中有此字段)
    'trigger_point_name': '触点',  # 触点名称

    # 基础指标
    'convert_uv': '转化UV',
    'convert_rate': '曝光转化率',  # 对应表格中的曝光转化率
    'convert_new_rate': '新客占比',  # 对应表格中的新客占比
    'average_transaction_value': '客单价',  # 对应表格中的客单价

    # 基准指标 (bm_前缀)
    'bm_convert_uv': '参考转化UV',
    'bm_convert_rate': '参考曝光转化率',
    'bm_convert_new_rate': '参考新客占比',
    'bm_average_transaction_value': '参考客单价',

    # 元数据
    'data_period': '数据周期',  # 使用数据周期字段存储月份（如2025/05）
}

# 数据类型映射 - 基于实际表格结构
FIELD_TYPES = {
    '触点ID': 'text',    # 字段类型1 - 文本
    '触点': 'text',    # 字段类型1 - 文本
    '转化UV': 'number',  # 字段类型2 - 数字
    '曝光转化率': 'number',  # 字段类型2 - 数字
    '新客占比': 'number',   # 字段类型2 - 数字
    '客单价': 'number',     # 字段类型2 - 数字
    '参考转化UV': 'number',  # 字段类型2 - 数字
    '参考曝光转化率': 'number',  # 字段类型2 - 数字
    '参考新客占比': 'number',   # 字段类型2 - 数字
    '参考客单价': 'number',     # 字段类型2 - 数字
    '数据周期': 'text'      # 字段类型1 - 文本，存储月份格式（如2025/05）
}

# 历史数据获取配置
HISTORICAL_DATA_CONFIG = {
    'start_year': 2024,  # 开始年份
    'start_month': 1,    # 开始月份
    'max_months_back': 24,  # 最多获取多少个月的历史数据
}
