# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Environment Variables
.env
.env.local
.env.production
.env.staging

# Logs
logs/
*.log
*.log.*

# Downloaded Files
downloads/
*.xlsx
*.xls
*.csv

# Backups
backups/

# Cache
.cache/
*.cache

# OS Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary Files
temp/
tmp/
*.tmp
*.temp

# Cookie Files
cookies/
*.cookies

# Test Files
test_data/
test_downloads/
test_*.xlsx

# Configuration Backups
config/*.bak
config/*.backup

# Runtime Files
*.pid
*.lock

# Coverage Reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Database
*.db
*.sqlite
*.sqlite3

# Jupyter Notebooks
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# Windows
desktop.ini 