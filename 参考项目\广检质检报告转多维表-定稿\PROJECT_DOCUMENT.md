# GTTC 报告数据自动同步系统 - 项目文档

**版本**: v1.0  
**创建日期**: 2024年1月  
**文档类型**: PRD + 技术文档  

---

## 📋 项目概述

### 项目背景
广东省测试技术研究所（GTTC）作为权威的纺织品质量检测机构，每日产生大量的质检报告数据。这些数据目前需要人工查看和整理，效率低下且容易遗漏。为了提升工作效率和数据管理质量，需要开发一套自动化的数据同步系统。

### 项目目标
- **主要目标**: 实现GTTC报告数据到飞书多维表的自动化同步
- **次要目标**: 提供附件自动下载和管理功能
- **长期目标**: 建立可扩展的数据同步平台，支持多数据源集成

### 核心价值主张
1. **效率提升**: 从人工操作转为自动化，节省90%的数据录入时间
2. **数据准确性**: 消除人工录入错误，确保数据一致性
3. **实时性**: 每小时自动同步，确保数据时效性
4. **可追溯性**: 完整的操作日志，便于问题排查和审计

---

## 🎯 产品需求分析 (PRD)

### 用户故事

#### 主要用户角色
- **质检管理员**: 需要查看和管理所有质检报告数据
- **业务人员**: 需要跟踪特定委托单位的报告进度
- **财务人员**: 需要查看费用和缴费状态信息

#### 核心用户故事
1. **作为质检管理员**，我希望能在飞书多维表中实时查看所有GTTC报告数据，以便及时了解检测进度和结果
2. **作为业务人员**，我希望报告附件能自动下载到飞书，以便快速查阅相关文档
3. **作为系统管理员**，我希望系统能自动运行并提供详细日志，以便监控和维护

### 功能需求

#### 核心功能需求 (P0)
1. **数据抓取功能**
   - 从GTTC网站自动抓取报告数据
   - 支持日期范围筛选（默认30天）
   - 处理网站反爬机制和网络异常

2. **数据同步功能**
   - 将抓取的数据同步到飞书多维表
   - 支持增量更新，避免重复数据
   - 自动处理数据类型转换和字段映射

3. **附件管理功能**
   - 自动下载受理单、缴费通知单、报告书
   - 上传附件到飞书云空间
   - 智能判断下载时机（如报告书需要已出证）

4. **定时执行功能**
   - 支持每小时自动执行
   - 容器启动时立即执行一次
   - 提供整点执行和间隔执行两种模式

#### 重要功能需求 (P1)
1. **数据质量保证**
   - 数据一致性检查
   - 异常数据标记和处理
   - 执行结果统计和报告

2. **容错和重试机制**
   - 网络异常自动重试
   - 文件下载失败重试
   - 飞书API调用失败重试

3. **日志和监控**
   - 详细的执行日志
   - 实时状态显示
   - 异常情况告警

#### 扩展功能需求 (P2)
1. **配置管理**
   - 可配置的字段映射
   - 可调整的执行频率
   - 灵活的过滤条件

2. **数据分析**
   - 同步成功率统计
   - 性能指标监控
   - 数据趋势分析

### 非功能需求

#### 性能需求
- **响应时间**: 单次同步操作在5分钟内完成
- **并发处理**: 支持5个并发文件上传
- **数据量**: 支持每次处理1000+条记录

#### 可靠性需求
- **可用性**: 99%的运行时间保证
- **数据准确性**: 99.9%的数据同步准确率
- **容错能力**: 网络或API异常时自动重试

#### 安全需求
- **认证安全**: 飞书API密钥安全存储
- **数据传输**: HTTPS加密传输
- **访问控制**: 合理的权限设置

#### 可扩展性需求
- **水平扩展**: 支持Docker容器化部署
- **功能扩展**: 模块化设计，便于添加新功能
- **数据源扩展**: 架构支持接入其他数据源

---

## 🏗️ 技术架构设计

### 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   GTTC 网站     │    │   同步服务      │    │   飞书多维表     │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │  质检报告   │ │───▶│ │ 数据抓取器  │ │    │ │   数据表    │ │
│ │    页面     │ │    │ │  (Scraper)  │ │    │ │  (Table)    │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │        │        │    │        ▲        │
│ ┌─────────────┐ │    │        ▼        │    │        │        │
│ │  附件下载   │ │───▶│ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │    链接     │ │    │ │ 数据处理器  │ │────▶│ │ 附件存储    │ │
│ └─────────────┘ │    │ │ (Processor) │ │    │ │ (Storage)   │ │
└─────────────────┘    │ └─────────────┘ │    │ └─────────────┘ │
                       │        │        │    └─────────────────┘
┌─────────────────┐    │        ▼        │
│   定时调度器     │    │ ┌─────────────┐ │
│  (Scheduler)    │────▶│ │ 飞书上传器  │ │
│                 │    │ │ (Uploader)  │ │
│  - 整点执行     │    │ └─────────────┘ │
│  - 立即启动     │    └─────────────────┘
└─────────────────┘
```

### 核心模块设计

#### 1. 数据抓取模块 (gttc_scraper.py)
**职责**: 从GTTC网站抓取和解析数据

**核心类**: `GTTCReportScraper`
```python
class GTTCReportScraper:
    def __init__(self):
        # 初始化session、设置请求头等
    
    def make_request(self, params):
        # 发送HTTP请求，处理网络异常
    
    def parse_html_table(self, html_content):
        # 解析HTML表格，提取数据
    
    def extract_download_links(self, row):
        # 提取附件下载链接
```

**关键技术点**:
- 使用`requests.Session`保持连接复用
- `BeautifulSoup`解析HTML结构
- 自动处理相对URL转绝对URL
- 网络异常重试机制

#### 2. 飞书集成模块 (feishu_uploader_utils.py)
**职责**: 封装飞书API调用，处理认证和数据操作

**核心函数**:
```python
def get_tenant_access_token():
    # 获取飞书访问令牌

def get_bitable_records(app_token, table_id, access_token):
    # 获取多维表记录，支持分页

def batch_add_records(app_token, table_id, records, access_token):
    # 批量添加记录

def batch_update_records(app_token, table_id, records, access_token):
    # 批量更新记录
```

**关键技术点**:
- OAuth2.0认证流程
- 批量操作优化（每次最多500条）
- API调用频率限制处理
- 错误重试和降级策略

#### 3. 主业务逻辑模块 (gttc_to_feishu_uploader.py)
**职责**: 协调各模块，实现完整的数据同步流程

**核心流程**:
```python
def main():
    # 1. 获取飞书访问令牌
    # 2. 抓取GTTC数据
    # 3. 获取飞书现有记录
    # 4. 增量更新逻辑
    # 5. 并发处理和统计
```

**关键技术点**:
- 多线程并发处理（ThreadPoolExecutor）
- 增量更新算法
- 附件下载和上传管理
- 详细的执行统计

#### 4. 定时调度模块 (scheduler.py)
**职责**: 管理定时执行和容器生命周期

**核心功能**:
- 整点执行vs间隔执行
- 启动时立即执行配置
- 分段等待和进度显示
- 异常处理和重试

### 数据流设计

#### 数据抓取流程
```
GTTC网站 → HTTP请求 → HTML解析 → 数据提取 → 字段映射 → 标准化数据
```

#### 附件处理流程
```
附件链接 → 下载判断 → 文件下载 → 飞书上传 → file_token → 记录关联
```

#### 增量更新流程
```
GTTC数据 → 飞书现有数据 → 字段比较 → 新增/更新/跳过 → 批量操作
```

---

## 💻 技术实现细节

### 技术栈选择

#### 后端技术栈
- **Python 3.9+**: 主要开发语言，成熟的生态系统
- **requests**: HTTP客户端，支持会话管理和连接池
- **BeautifulSoup + lxml**: HTML解析，高性能且容错性好
- **concurrent.futures**: 多线程并发处理

#### 部署技术栈
- **Docker**: 容器化部署，确保环境一致性
- **Zeabur/Railway**: 云平台部署，支持CI/CD
- **GitHub Actions**: 自动化构建和部署

### 关键算法设计

#### 1. 增量更新算法
```python
def is_record_changed(gttc_record, feishu_record):
    """
    比较GTTC记录和飞书记录是否有变化
    
    算法核心:
    1. 字段级比较：逐个字段对比值
    2. 类型标准化：统一数据类型后比较
    3. 附件特殊处理：基于链接有效性判断
    """
    for field, value in gttc_record.items():
        if not is_equivalent(value, feishu_record.get(field)):
            return True
    return False
```

#### 2. 附件下载决策算法
```python
def should_download_attachment(attachment_type, report_status):
    """
    根据附件类型和报告状态决定是否下载
    
    决策矩阵:
    - 受理单: 总是下载
    - 缴费通知单: 总是下载
    - 报告书: 仅当"已出证"且"可下载"时下载
    """
    if attachment_type in ['受理单', '缴费通知单']:
        return True
    elif attachment_type == '报告书':
        return (report_status['检测进度'] == '已出证' and 
                report_status['报告书'] == '可下载')
    return False
```

#### 3. 并发控制算法
```python
# 使用信号量控制并发数
UPLOAD_SEMAPHORE = threading.Semaphore(5)  # 最多5个并发上传

# 使用延迟避免API频率限制
def rate_limited_upload(file_path):
    with UPLOAD_SEMAPHORE:
        result = upload_file(file_path)
        time.sleep(0.2 + random.uniform(0, 0.1))  # 随机延迟
        return result
```

### 数据结构设计

#### GTTC数据结构
```python
gttc_record = {
    '序号': int,
    '报告编号': str,
    '受理时间': str,
    '委托单位': str,
    '样品名称': str,
    '款号': str,
    '颜色': str,
    '商标': str,
    '面料编号': str,
    '生产单位': str,
    '缴费单位': str,
    '检验项目': str,
    '检测进度': str,  # '已受理', '正在检测', '已出证'
    '报告结果': str,  # '合格', '不合格'
    '不合格项目': str,
    '预计发证时间': str,
    '出证时间': str,
    '金额': float,
    '费用交付状态': str,
    '缴费通知单': str,  # '已发放', '不可用'
    '报告书': str,     # '可下载', '不可用'
    '报告寄出信息': str,
    '备注': str,
    '受理单下载链接': str,
    '缴费通知单下载链接': str,
    '报告书下载链接': str
}
```

#### 飞书数据结构
```python
feishu_record = {
    'fields': {
        '报告编号': str,
        '委托单位': str,
        # ... 其他文本字段
        '金额': int,  # 数字字段
        '受理单附件': [{'file_token': str}],  # 附件字段
        '缴费通知单附件': [{'file_token': str}],
        '报告书附件': [{'file_token': str}]
    }
}
```

### 配置管理设计

#### 环境变量配置
```python
# feishu_config.py
import os
from dotenv import load_dotenv

load_dotenv()

# 飞书应用配置
APP_ID = os.getenv('FEISHU_APP_ID')
APP_SECRET = os.getenv('FEISHU_APP_SECRET')
APP_TOKEN = os.getenv('FEISHU_APP_TOKEN')
TABLE_ID = os.getenv('FEISHU_TABLE_ID')

# 字段映射配置
FIELD_MAPPING = {
    '报告编号': '报告编号',
    '委托单位': '委托单位',
    # ... 完整的字段映射
}
```

#### 调度器配置
```python
# scheduler.py 配置项
EXECUTE_ON_HOUR = True    # 整点执行模式
IMMEDIATE_START = True    # 启动时立即执行
SYNC_INTERVAL = 3600      # 同步间隔（秒）
```

---

## 🔄 业务流程设计

### 完整业务流程

```mermaid
graph TD
    A[容器启动] --> B[调度器初始化]
    B --> C{配置检查}
    C -->|配置有误| D[报错退出]
    C -->|配置正确| E[立即执行同步]
    
    E --> F[获取飞书Token]
    F --> G[抓取GTTC数据]
    G --> H[获取飞书现有数据]
    H --> I[数据比较和分类]
    
    I --> J{有新增记录?}
    J -->|是| K[处理新增记录]
    J -->|否| L{有更新记录?}
    
    K --> M[下载附件]
    M --> N[上传到飞书]
    N --> O[添加记录]
    
    L -->|是| P[处理更新记录]
    L -->|否| Q[输出统计信息]
    
    P --> R[下载新附件]
    R --> S[更新记录]
    S --> Q
    O --> Q
    
    Q --> T[等待下次执行]
    T --> U{到达执行时间?}
    U -->|是| F
    U -->|否| T
```

### 异常处理流程

```mermaid
graph TD
    A[操作执行] --> B{是否成功?}
    B -->|成功| C[继续下一步]
    B -->|失败| D{重试次数<最大值?}
    D -->|是| E[等待重试延迟]
    E --> F[重试操作]
    F --> B
    D -->|否| G[记录错误日志]
    G --> H[跳过当前操作]
    H --> C
```

### 数据同步决策流程

```mermaid
graph TD
    A[GTTC记录] --> B{飞书中存在?}
    B -->|否| C[标记为新增]
    B -->|是| D[字段逐一比较]
    D --> E{有字段变化?}
    E -->|否| F[跳过更新]
    E -->|是| G[检查附件变化]
    G --> H{附件有变化?}
    H -->|是| I[标记为更新+附件]
    H -->|否| J[标记为仅更新数据]
    
    C --> K[下载所有附件]
    I --> L[下载新增附件]
    J --> M[保留现有附件]
    F --> N[无操作]
    
    K --> O[添加飞书记录]
    L --> P[更新飞书记录]
    M --> P
    N --> Q[统计结果]
    O --> Q
    P --> Q
```

---

## 📊 数据模型设计

### 实体关系图

```
┌─────────────────┐
│   GTTC报告      │
├─────────────────┤
│ + 报告编号 (PK) │
│ + 委托单位      │
│ + 样品名称      │
│ + 检测进度      │
│ + 报告结果      │
│ + 金额          │
│ + 受理时间      │
│ + 出证时间      │
└─────────────────┘
         │
         │ 1:N
         ▼
┌─────────────────┐
│   报告附件      │
├─────────────────┤
│ + 附件ID (PK)   │
│ + 报告编号 (FK) │
│ + 附件类型      │
│ + 下载链接      │
│ + 文件Token     │
│ + 上传状态      │
└─────────────────┘
```

### 字段映射表

| GTTC字段 | 飞书字段 | 数据类型 | 特殊处理 |
|----------|----------|----------|----------|
| 序号 | 序号 | 数字 | 自动递增 |
| 报告编号 | 报告编号 | 文本 | 主键 |
| 委托单位 | 委托单位 | 单选 | 选项动态创建 |
| 金额 | 金额 | 数字 | 千分位处理 |
| 受理单下载链接 | 受理单附件 | 附件 | 下载+上传 |
| 缴费通知单下载链接 | 缴费通知单附件 | 附件 | 下载+上传 |
| 报告书下载链接 | 报告书附件 | 附件 | 条件下载 |

### 状态机设计

#### 报告处理状态
```
[待处理] → [数据抓取] → [字段映射] → [附件下载] → [飞书上传] → [完成]
    ↓           ↓           ↓           ↓           ↓
  [错误]    [抓取失败]  [映射错误]  [下载失败]  [上传失败]
    ↓           ↓           ↓           ↓           ↓
  [重试]      [重试]      [跳过]      [重试]      [重试]
```

#### 附件处理状态
```
[检测链接] → [判断条件] → [开始下载] → [上传飞书] → [清理临时文件] → [完成]
     ↓           ↓           ↓           ↓              ↓
  [链接无效]  [条件不满足] [下载失败] [上传失败]     [清理失败]
     ↓           ↓           ↓           ↓              ↓
   [跳过]      [跳过]      [重试]      [重试]        [继续]
```

---

## 🚀 部署架构设计

### 容器化架构

```
┌─────────────────────────────────────────────────────────┐
│                    Docker Container                     │
├─────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │ scheduler.py│  │ gttc_scraper│  │feishu_utils │     │
│  │             │  │   .py       │  │    .py      │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
│         │                │                │             │
│         └────────────────┼────────────────┘             │
│                          │                              │
│  ┌─────────────────────────────────────────────────────┐│
│  │        gttc_to_feishu_uploader.py                  ││
│  │              (主业务逻辑)                          ││
│  └─────────────────────────────────────────────────────┘│
│                          │                              │
│  ┌─────────────────────────────────────────────────────┐│
│  │                  环境变量                           ││
│  │  - FEISHU_APP_ID                                   ││
│  │  - FEISHU_APP_SECRET                               ││
│  │  - FEISHU_APP_TOKEN                                ││
│  │  - FEISHU_TABLE_ID                                 ││
│  └─────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────┐
│                   云平台服务                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │   Zeabur    │  │   Railway   │  │    Render   │     │
│  │             │  │             │  │             │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
└─────────────────────────────────────────────────────────┘
```

### CI/CD流程

```mermaid
graph LR
    A[代码提交] --> B[GitHub触发]
    B --> C[构建Docker镜像]
    C --> D[运行测试]
    D --> E{测试通过?}
    E -->|是| F[推送到Registry]
    E -->|否| G[通知失败]
    F --> H[自动部署]
    H --> I[健康检查]
    I --> J{部署成功?}
    J -->|是| K[部署完成]
    J -->|否| L[回滚版本]
```

### 监控和日志架构

```
应用日志 → 标准输出 → 容器日志 → 平台日志系统 → 监控告警
    ↓
本地调试 → 控制台输出 → 开发者查看
```

---

## 🔧 运维和监控

### 关键指标监控

#### 业务指标
- **同步成功率**: 成功同步的记录数 / 总记录数
- **数据一致性**: 飞书数据与GTTC数据的一致性检查
- **附件下载成功率**: 成功下载的附件数 / 应下载附件数
- **执行频率**: 实际执行间隔 vs 预期执行间隔

#### 技术指标
- **响应时间**: 单次同步操作的耗时
- **内存使用**: 容器内存使用情况
- **网络延迟**: 到GTTC网站和飞书API的网络延迟
- **错误率**: 各类异常的发生频率

#### 日志分析
```python
# 日志格式设计
log_format = {
    "timestamp": "2024-01-XX 14:30:15",
    "level": "INFO|ERROR|WARNING",
    "module": "scraper|uploader|scheduler",
    "operation": "sync|download|upload",
    "report_id": "报告编号",
    "message": "详细描述",
    "duration": "执行耗时(秒)",
    "status": "success|failed|retry"
}
```

### 故障处理手册

#### 常见故障及解决方案

1. **GTTC网站访问失败**
   - 现象: 抓取数据时网络连接超时
   - 排查: 检查网络连接、GTTC网站状态
   - 解决: 增加重试次数、调整超时时间

2. **飞书API调用失败**
   - 现象: 获取token失败或数据上传失败
   - 排查: 检查APP_ID/SECRET配置、权限设置
   - 解决: 重新配置认证信息、检查应用权限

3. **附件下载失败**
   - 现象: 下载链接访问失败或文件损坏
   - 排查: 检查链接有效性、网络状况
   - 解决: 跳过无效链接、增加重试机制

4. **容器内存不足**
   - 现象: 容器被杀死或性能下降
   - 排查: 监控内存使用情况
   - 解决: 增加容器内存限制、优化代码

#### 应急响应流程

```mermaid
graph TD
    A[故障发现] --> B[影响评估]
    B --> C{严重程度}
    C -->|高| D[立即停止服务]
    C -->|中| E[降级运行]
    C -->|低| F[继续监控]
    
    D --> G[问题定位]
    E --> G
    F --> H[记录问题]
    
    G --> I[制定解决方案]
    I --> J[实施修复]
    J --> K[验证修复]
    K --> L{修复成功?}
    L -->|是| M[恢复服务]
    L -->|否| N[回滚处理]
    
    M --> O[总结报告]
    N --> G
    H --> O
```

---

## 📈 项目管理

### 开发计划

#### 第一阶段: MVP版本 (已完成)
- ✅ 基础数据抓取功能
- ✅ 飞书API集成
- ✅ 简单的数据同步
- ✅ Docker容器化

#### 第二阶段: 增强版本 (已完成)
- ✅ 附件下载和上传
- ✅ 增量更新机制
- ✅ 定时执行功能
- ✅ 错误处理和重试

#### 第三阶段: 优化版本 (当前)
- ✅ 详细日志和监控
- ✅ 性能优化
- ✅ 配置管理
- 🔄 文档完善

#### 第四阶段: 扩展版本 (规划中)
- 📋 Web管理界面
- 📋 数据分析报表
- 📋 多数据源支持
- 📋 消息通知功能

### 风险评估

#### 技术风险
- **网站结构变化**: GTTC网站改版导致解析失败
  - 缓解: 定期检查、快速响应机制
- **API限制变化**: 飞书API政策调整
  - 缓解: 关注官方公告、保持版本更新
- **性能瓶颈**: 数据量增长导致性能下降
  - 缓解: 性能监控、优化算法

#### 业务风险
- **数据丢失**: 同步过程中数据丢失
  - 缓解: 备份机制、事务处理
- **数据错误**: 同步的数据有误
  - 缓解: 数据验证、人工抽查
- **服务中断**: 系统故障导致服务不可用
  - 缓解: 健康检查、自动重启

### 成功指标

#### 短期指标 (1个月)
- 系统稳定运行，可用性 > 95%
- 数据同步准确率 > 99%
- 用户满意度调研 > 4.0/5.0

#### 中期指标 (3个月)
- 减少人工数据录入工作量 80%
- 数据处理时效性提升 90%
- 零严重故障事件

#### 长期指标 (6个月)
- 支持更多数据源接入
- 建立完整的数据分析体系
- 成为团队数据管理的标准工具

---

## 📚 附录

### 参考资料
- [飞书开放平台文档](https://open.feishu.cn/document/)
- [Python requests文档](https://docs.python-requests.org/)
- [BeautifulSoup文档](https://www.crummy.com/software/BeautifulSoup/bs4/doc/)
- [Docker最佳实践](https://docs.docker.com/develop/best-practices/)

### 版本历史
- **v1.0.0** (2024-01-XX): 初始版本，基础功能完成
- **v1.1.0** (2024-01-XX): 添加附件管理和增量更新
- **v1.2.0** (2024-01-XX): 优化调度器和日志系统

### 团队联系
- **项目负责人**: [负责人姓名]
- **技术负责人**: [技术负责人姓名]
- **运维负责人**: [运维负责人姓名]

---

**文档状态**: 当前版本  
**最后更新**: 2024年1月  
**下次审查**: 2024年2月 