#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CookieCloud 简单测试脚本
使用官方PyCookieCloud库验证功能
"""

from PyCookieCloud import PyCookieCloud
import json

def main():
    """主函数"""
    print("🍪 CookieCloud 官方库测试工具")
    print("=" * 50)
    
    # 配置信息
    SERVER_URL = "https://cnarbguabrg.zeabur.app"
    UUID = "5csGiUGWFge13h6N3pvXYo"
    PASSWORD = "31KEAXegEv5Khth6oLZ8fW"
    
    print(f"🌐 服务器地址: {SERVER_URL}")
    print(f"🔑 UUID: {UUID}")
    print(f"🔐 密码: {PASSWORD[:10]}...")
    
    try:
        # 创建CookieCloud客户端
        print("\n🚀 创建CookieCloud客户端...")
        cookie_cloud = PyCookieCloud(SERVER_URL, UUID, PASSWORD)
        
        # 获取密钥
        print("📝 获取密钥...")
        the_key = cookie_cloud.get_the_key()
        if not the_key:
            print('❌ 获取密钥失败')
            return
        print("✅ 密钥获取成功")
        
        # 获取加密数据
        print("📡 获取加密数据...")
        encrypted_data = cookie_cloud.get_encrypted_data()
        if not encrypted_data:
            print('❌ 获取加密数据失败')
            return
        print("✅ 加密数据获取成功")
        
        # 获取解密数据
        print("🔓 解密数据...")
        decrypted_data = cookie_cloud.get_decrypted_data()
        if not decrypted_data:
            print('❌ 解密数据失败')
            return
        print("✅ 数据解密成功")
        
        # 显示数据统计
        print("\n" + "="*60)
        print("🍪 CookieCloud 数据概览")
        print("="*60)
        
        # 显示基本信息
        if 'update_time' in decrypted_data:
            print(f"📅 更新时间: {decrypted_data['update_time']}")
        
        # 统计cookie信息
        total_cookies = 0
        domains = []
        
        for domain, cookies in decrypted_data.items():
            if domain == 'update_time':
                continue
            if isinstance(cookies, list):
                total_cookies += len(cookies)
                domains.append((domain, len(cookies)))
        
        print(f"🌐 域名数量: {len(domains)}")
        print(f"🍪 Cookie总数: {total_cookies}")
        
        # 显示各域名的cookie数量（前10个）
        print("\n📊 各域名Cookie统计 (前10个):")
        print("-" * 40)
        
        # 按cookie数量排序
        domains.sort(key=lambda x: x[1], reverse=True)
        
        for i, (domain, count) in enumerate(domains[:10]):
            print(f"  {i+1:2d}. {domain}: {count} 个cookie")
        
        if len(domains) > 10:
            print(f"  ... 还有 {len(domains) - 10} 个域名")
        
        # 显示部分cookie详情（前3个域名）
        print("\n🔍 Cookie详情预览 (前3个域名):")
        print("-" * 40)
        
        for i, (domain, _) in enumerate(domains[:3]):
            cookies = decrypted_data[domain]
            if isinstance(cookies, list) and cookies:
                print(f"\n🌐 {domain}:")
                for j, cookie in enumerate(cookies[:3]):  # 每个域名最多显示3个cookie
                    name = cookie.get('name', 'N/A')
                    value = cookie.get('value', 'N/A')
                    # 截断过长的值
                    if len(value) > 50:
                        value = value[:47] + "..."
                    print(f"  [{j+1}] {name} = {value}")
                
                if len(cookies) > 3:
                    print(f"  ... 还有 {len(cookies) - 3} 个cookie")
        
        # 保存到文件
        output_file = "cookies_data_official.json"
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(decrypted_data, f, ensure_ascii=False, indent=2)
            print(f"\n💾 数据已保存到: {output_file}")
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
        
        print("\n✅ 测试完成！CookieCloud服务可正常使用")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        print(f"🔍 错误类型: {type(e).__name__}")

if __name__ == "__main__":
    main() 