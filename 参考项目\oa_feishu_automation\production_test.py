#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Production-ready test script for OA to Feishu automation
"""

import sys
import os
from pathlib import Path

# Add current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """Test if basic modules can be imported"""
    print("🧪 Testing basic imports...")
    
    try:
        import requests
        import pandas as pd
        from pathlib import Path
        import json
        import time
        print("✅ Basic packages imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Basic import error: {e}")
        return False

def test_project_imports():
    """Test if project modules can be imported"""
    print("\n🔧 Testing project module imports...")
    
    try:
        from config.settings import settings
        print("✅ Config settings imported")
        
        from utils.logger import setup_logger
        print("✅ Logger imported")
        
        from utils.date_utils import get_date_range, get_current_timestamp
        print("✅ Date utils imported")
        
        from utils.validators import clean_text, validate_amount
        print("✅ Validators imported")
        
        return True
        
    except ImportError as e:
        print(f"❌ Project import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_core_modules():
    """Test core modules with mock dependencies"""
    print("\n🏗️ Testing core modules...")
    
    try:
        # Test with mock cookie manager first
        from core.mock_cookie_manager import MockCookieManager
        mock_cookie_manager = MockCookieManager()
        print("✅ MockCookieManager imported and initialized")
        
        # Test cookie loading
        if mock_cookie_manager.load_cookies():
            print("✅ Mock cookies loaded successfully")
        else:
            print("⚠️  Mock cookie loading failed")
        
        # Test OA client with mock cookies
        from core.oa_client import OAClient
        oa_client = OAClient(mock_cookie_manager)
        print("✅ OAClient imported and initialized with mock cookies")
        
        # Test Excel processor
        from core.excel_processor import ExcelProcessor
        excel_processor = ExcelProcessor()
        print("✅ ExcelProcessor imported and initialized")
        
        # Test Feishu client (without actual API calls)
        from core.feishu_client import FeishuClient
        feishu_client = FeishuClient()
        print("✅ FeishuClient imported and initialized")
        
        return True
        
    except ImportError as e:
        print(f"❌ Core module import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Core module error: {e}")
        return False

def test_configuration():
    """Test configuration loading"""
    print("\n⚙️  Testing configuration...")
    
    try:
        from config.settings import settings
        
        print(f"CookieCloud URL: {settings.cookiecloud.server_url}")
        print(f"Feishu Base ID: {settings.feishu.base_id}")
        print(f"OA Base URL: {settings.oa.base_url}")
        
        # Test field mappings
        from config.field_mappings import FIELD_MAPPINGS
        print(f"Field mappings loaded: {len(FIELD_MAPPINGS)} report types")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_utilities():
    """Test utility functions"""
    print("\n🛠️ Testing utility functions...")
    
    try:
        from utils.date_utils import get_date_range, get_current_timestamp
        from utils.validators import clean_text, validate_amount
        from utils.logger import setup_logger
        
        # Test date utilities
        start_date, end_date = get_date_range(30)
        print(f"✅ Date range: {start_date} to {end_date}")
        
        # Test timestamp
        timestamp = get_current_timestamp()
        print(f"✅ Current timestamp: {timestamp}")
        
        # Test validators
        cleaned = clean_text("  test text  ")
        print(f"✅ Text cleaning: '{cleaned}'")
        
        amount_valid = validate_amount("123.45")
        print(f"✅ Amount validation: {amount_valid}")
        
        # Test logger
        logger = setup_logger(level="INFO")
        logger.info("Test log message")
        print("✅ Logger working")
        
        return True
        
    except Exception as e:
        print(f"❌ Utility function error: {e}")
        return False

def test_main_application():
    """Test main application import"""
    print("\n🚀 Testing main application...")
    
    try:
        from main import OAFeishuAutomation
        print("✅ Main application class imported")
        
        # Don't initialize to avoid dependency issues
        print("✅ Main application ready for initialization")
        
        return True
        
    except ImportError as e:
        print(f"❌ Main application import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Main application error: {e}")
        return False

def test_directories():
    """Test directory creation"""
    print("\n📁 Testing directory creation...")
    
    try:
        # Create test directories
        test_dirs = ["downloads", "logs", "backups"]
        
        for dir_name in test_dirs:
            Path(dir_name).mkdir(exist_ok=True)
            if Path(dir_name).exists():
                print(f"✅ Directory created: {dir_name}")
            else:
                print(f"❌ Failed to create: {dir_name}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Directory creation error: {e}")
        return False

def test_dependencies():
    """Test if all required dependencies are available"""
    print("\n📦 Testing dependencies...")
    
    required_packages = [
        ('requests', 'requests'),
        ('pandas', 'pandas'),
        ('openpyxl', 'openpyxl'),
        ('python-dotenv', 'dotenv'),
        ('beautifulsoup4', 'bs4'),
        ('lxml', 'lxml'),
        ('urllib3', 'urllib3')
    ]

    missing_packages = []

    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"✅ {package_name}")
        except ImportError:
            print(f"❌ {package_name} - MISSING")
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    print("✅ All required dependencies available")
    return True

def main():
    """Main test function"""
    print("🚀 OA to Feishu Automation - Production Test")
    print("="*60)
    
    tests = [
        ("Dependencies", test_dependencies),
        ("Basic Imports", test_basic_imports),
        ("Project Imports", test_project_imports),
        ("Configuration", test_configuration),
        ("Utilities", test_utilities),
        ("Core Modules", test_core_modules),
        ("Main Application", test_main_application),
        ("Directories", test_directories),
    ]
    
    results = {}
    all_passed = True
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running: {test_name}")
        print("="*60)
        
        try:
            result = test_func()
            results[test_name] = result
            if not result:
                all_passed = False
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results[test_name] = False
            all_passed = False
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 Test Results Summary")
    print("="*60)
    
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name:20} {status}")
    
    print("\n" + "="*60)
    if all_passed:
        print("🎉 All tests passed! Project is ready for production.")
        print("💡 Next steps:")
        print("   1. Configure .env file with actual credentials")
        print("   2. Install PyCookieCloud if using real CookieCloud")
        print("   3. Run: python main.py --validate-only")
        print("   4. Run: python main.py")
    else:
        print("⚠️  Some tests failed. Please fix the issues above.")
        print("📖 Check the error messages and install missing dependencies.")
    print("="*60)
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
