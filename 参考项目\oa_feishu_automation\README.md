# OA to Feishu Automation

自动化从OA系统获取报表数据并上传到飞书多维表的Python应用程序。

## 🚀 功能特性

- **自动Cookie管理**: 使用CookieCloud自动获取最新的认证Cookie
- **OA API集成**: 模拟HTTP请求下载OA系统的Excel报表
- **数据处理**: 解析Excel文件并提取相关数据
- **增量上传**: 支持增量更新，避免重复数据
- **多报表支持**: 支持合同用印审批、付款申请单、预付款申请单
- **错误处理**: 完善的错误处理和日志记录

## 📋 支持的报表类型

1. **合同用印审批** (report_id: 142)
2. **付款申请单** (report_id: 215)
3. **预付款申请单** (report_id: 216)

## 🛠️ 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

复制 `.env.example` 到 `.env` 并填写配置信息：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
# CookieCloud配置
COOKIECLOUD_SERVER_URL=https://your-cookiecloud-server.com
COOKIECLOUD_UUID=your-uuid
COOKIECLOUD_PASSWORD=your-password

# 飞书配置
FEISHU_APP_ID=your-app-id
FEISHU_APP_SECRET=your-app-secret
FEISHU_BASE_ID=PMatbmyEkae3YCsqPJIcOL0AnBh
FEISHU_CONTRACT_TABLE_ID=tbl3GijdiVYaJ4YK
FEISHU_PAYMENT_TABLE_ID=tbl4uI2rzYCjoTHu

# OA配置
OA_BASE_URL=http://oa.yaduo.com
OA_DOMAIN=oa.yaduo.com
```

### 3. 配置字段映射

根据实际的Excel文件结构，编辑 `config/field_mappings.py` 中的字段映射配置。

## 🎯 快速开始

### 1. 部署和安装

```bash
# 运行自动部署脚本
python deploy.py

# 或手动安装
pip install -r requirements.txt
```

### 2. 配置环境

编辑 `.env` 文件，填写必要的配置信息。

### 3. 验证设置

```bash
# 运行设置测试
python test_setup.py

# 分析Excel文件结构（可选）
python analyze_excel.py
```

### 4. 运行自动化

```bash
# 处理所有报表类型（最近30天）
python main.py

# 处理特定报表类型
python main.py --report-type contract

# 指定日期范围（最近7天）
python main.py --days 7

# 仅验证配置
python main.py --validate-only

# 获取飞书表格结构
python main.py --get-schema
```

### 5. 使用便捷脚本

```bash
# Windows
run.bat

# Unix/Linux
./run.sh
```

### 命令行参数

- `--report-type`: 报表类型 (`contract`, `payment`, `prepayment`, `all`)
- `--days`: 回溯天数 (默认: 30)
- `--validate-only`: 仅验证配置
- `--get-schema`: 获取飞书表格结构信息
- `--log-level`: 日志级别 (`DEBUG`, `INFO`, `WARNING`, `ERROR`)

## 📁 项目结构

```text
oa_feishu_automation/
├── config/
│   ├── settings.py          # 配置管理
│   └── field_mappings.py    # 字段映射配置
├── core/
│   ├── cookie_manager.py    # CookieCloud集成
│   ├── oa_client.py         # OA API客户端
│   ├── excel_processor.py   # Excel文件处理
│   └── feishu_client.py     # 飞书API集成
├── utils/
│   ├── logger.py            # 日志配置
│   ├── date_utils.py        # 日期工具
│   └── validators.py        # 数据验证
├── main.py                  # 主程序入口
├── requirements.txt         # 依赖包
├── .env.example            # 环境变量模板
└── README.md               # 说明文档
```

## 🔧 核心组件

### CookieManager

- 从CookieCloud获取最新Cookie
- 验证OA系统认证状态
- 自动刷新过期Cookie

### OAClient

- 模拟OA系统API请求
- 下载Excel报表文件
- 处理导出任务状态

### ExcelProcessor

- 解析Excel文件结构
- 数据清洗和标准化
- 字段映射和验证

### FeishuClient

- 飞书API认证和请求
- 增量数据上传
- 重复记录检测和更新

## 📊 数据流程

1. **Cookie获取**: 从CookieCloud获取OA系统认证Cookie
2. **报表请求**: 向OA API发送报表数据请求
3. **Excel导出**: 请求Excel文件导出并等待完成
4. **文件下载**: 下载生成的Excel文件
5. **数据处理**: 解析Excel文件并映射字段
6. **增量上传**: 检测重复记录并执行增量上传到飞书

## 🔍 故障排查

### 常见问题

1. **CookieCloud连接失败**
   - 检查服务器地址、UUID和密码
   - 确认网络连接正常

2. **OA认证失败**
   - 确认浏览器已登录OA系统
   - 检查CookieCloud是否已同步最新Cookie

3. **飞书API错误**
   - 验证App ID和App Secret
   - 检查表格ID是否正确
   - 确认应用权限配置

4. **Excel处理错误**
   - 检查Excel文件格式
   - 验证字段映射配置
   - 查看详细错误日志

### 日志文件

日志文件保存在 `logs/` 目录下，按日期命名：

- `logs/oa_feishu_YYYYMMDD.log`

## 🔒 安全注意事项

- 妥善保管 `.env` 文件，不要提交到版本控制
- 定期更新CookieCloud密码
- 限制飞书应用权限范围
- 监控API调用频率

## 📈 性能优化

- 批量处理记录以提高上传效率
- 使用增量更新减少重复操作
- 合理设置重试机制和超时时间
- 定期清理下载的临时文件

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 📄 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 📞 支持

如有问题或建议，请创建Issue或联系开发团队。
