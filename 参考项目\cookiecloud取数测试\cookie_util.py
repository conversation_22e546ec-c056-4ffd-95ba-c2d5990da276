#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CookieCloud Cookie 获取工具
用于从CookieCloud中获取指定网站的cookie

使用方法:
1. 配置 .env 文件
2. python cookie_util.py <domain> [options]

示例:
python cookie_util.py github.com
python cookie_util.py github.com --format netscape
python cookie_util.py github.com --format requests --output github_cookies.py
"""

import os
import sys
import json
import argparse
from datetime import datetime
from urllib.parse import urlparse
from PyCookieCloud import PyCookieCloud
from dotenv import load_dotenv

class CookieUtil:
    def __init__(self):
        """初始化工具"""
        # 加载环境变量
        load_dotenv()
        
        self.server_url = os.getenv('COOKIECLOUD_SERVER_URL')
        self.uuid = os.getenv('COOKIECLOUD_UUID')
        self.password = os.getenv('COOKIECLOUD_PASSWORD')
        
        if not all([self.server_url, self.uuid, self.password]):
            raise ValueError("请在.env文件中配置CookieCloud信息")
        
        self.client = PyCookieCloud(self.server_url, self.uuid, self.password)
        self.all_cookies = None
    
    def load_cookies(self):
        """加载所有cookie数据"""
        if self.all_cookies is None:
            print("📡 正在从CookieCloud获取数据...")
            self.all_cookies = self.client.get_decrypted_data()
            if not self.all_cookies:
                raise Exception("获取cookie数据失败")
            print("✅ 数据获取成功")
        return self.all_cookies
    
    def get_domain_cookies(self, target_domain):
        """获取指定域名的cookies"""
        cookies = self.load_cookies()
        
        # 标准化域名
        if not target_domain.startswith(('.', 'http')):
            target_domain = target_domain.lower()
        
        matched_cookies = []
        
        # 遍历所有域名，查找匹配的cookies
        for domain, cookie_list in cookies.items():
            if domain == 'update_time':
                continue
            
            if isinstance(cookie_list, list):
                for cookie in cookie_list:
                    cookie_domain = cookie.get('domain', '').lower()
                    
                    # 匹配逻辑：
                    # 1. 完全匹配
                    # 2. 子域名匹配 (.example.com 匹配 example.com)
                    # 3. 父域名匹配 (example.com 匹配 sub.example.com)
                    if (cookie_domain == target_domain or
                        cookie_domain == f".{target_domain}" or
                        target_domain in cookie_domain or
                        cookie_domain.lstrip('.') == target_domain or
                        target_domain.endswith(cookie_domain.lstrip('.'))):
                        matched_cookies.append(cookie)
        
        return matched_cookies
    
    def format_json(self, cookies):
        """格式化为JSON"""
        return json.dumps(cookies, ensure_ascii=False, indent=2)
    
    def format_netscape(self, cookies):
        """格式化为Netscape格式 (适用于curl等工具)"""
        lines = ["# Netscape HTTP Cookie File"]
        lines.append("# Generated by CookieCloud Util")
        lines.append(f"# Generated at: {datetime.now().isoformat()}")
        lines.append("")
        
        for cookie in cookies:
            domain = cookie.get('domain', '')
            flag = 'TRUE' if domain.startswith('.') else 'FALSE'
            path = cookie.get('path', '/')
            secure = 'TRUE' if cookie.get('secure', False) else 'FALSE'
            
            # 处理过期时间
            expiration = cookie.get('expirationDate')
            if expiration:
                exp_timestamp = int(expiration)
            else:
                exp_timestamp = 0  # Session cookie
            
            name = cookie.get('name', '')
            value = cookie.get('value', '')
            
            line = f"{domain}\t{flag}\t{path}\t{secure}\t{exp_timestamp}\t{name}\t{value}"
            lines.append(line)
        
        return '\n'.join(lines)
    
    def format_requests(self, cookies, domain):
        """格式化为Python requests可用的代码"""
        cookie_dict = {}
        for cookie in cookies:
            name = cookie.get('name')
            value = cookie.get('value')
            if name and value:
                cookie_dict[name] = value
        
        code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
{domain} 网站的Cookie配置
由CookieCloud Util自动生成于 {datetime.now().isoformat()}
"""

import requests

# Cookie字典
COOKIES = {json.dumps(cookie_dict, ensure_ascii=False, indent=4)}

# 使用示例
def make_request_with_cookies(url):
    """使用cookies发送请求"""
    response = requests.get(url, cookies=COOKIES)
    return response

# 直接使用示例
if __name__ == "__main__":
    # 示例：访问{domain}
    url = "https://{domain}"
    response = make_request_with_cookies(url)
    print(f"状态码: {{response.status_code}}")
    print(f"响应长度: {{len(response.text)}}")
'''
        return code
    
    def save_to_file(self, content, filename):
        """保存内容到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"💾 已保存到: {filename}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='从CookieCloud获取指定网站的cookie',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
示例用法:
  %(prog)s github.com                           # 获取github.com的cookies (JSON格式)
  %(prog)s github.com --format netscape         # 获取Netscape格式
  %(prog)s github.com --format requests         # 生成Python requests代码
  %(prog)s github.com --output github.json      # 保存到文件
  %(prog)s github.com --list                    # 只显示cookie名称列表
        '''
    )
    
    parser.add_argument('domain', help='目标域名 (如: github.com)')
    parser.add_argument('--format', '-f', 
                       choices=['json', 'netscape', 'requests'],
                       default=os.getenv('DEFAULT_OUTPUT_FORMAT', 'json'),
                       help='输出格式 (默认: json)')
    parser.add_argument('--output', '-o', help='输出文件名')
    parser.add_argument('--list', '-l', action='store_true', help='只显示cookie名称列表')
    parser.add_argument('--verbose', '-v', action='store_true', help='显示详细信息')
    
    args = parser.parse_args()
    
    try:
        # 创建工具实例
        util = CookieUtil()
        
        if args.verbose:
            print(f"🌐 服务器: {util.server_url}")
            print(f"🔑 UUID: {util.uuid}")
            print(f"🎯 目标域名: {args.domain}")
            print(f"📋 输出格式: {args.format}")
            print()
        
        # 获取指定域名的cookies
        cookies = util.get_domain_cookies(args.domain)
        
        if not cookies:
            print(f"❌ 未找到域名 '{args.domain}' 的cookie")
            print("\n💡 提示:")
            print("1. 检查域名拼写是否正确")
            print("2. 确认浏览器中已登录该网站")
            print("3. 确认CookieCloud已同步最新数据")
            sys.exit(1)
        
        print(f"✅ 找到 {len(cookies)} 个cookie")
        
        # 如果只显示列表
        if args.list:
            print(f"\n🍪 {args.domain} 的Cookie列表:")
            for i, cookie in enumerate(cookies, 1):
                name = cookie.get('name', 'N/A')
                domain = cookie.get('domain', 'N/A')
                print(f"  {i:2d}. {name} (域名: {domain})")
            return
        
        # 格式化输出
        if args.format == 'json':
            content = util.format_json(cookies)
        elif args.format == 'netscape':
            content = util.format_netscape(cookies)
        elif args.format == 'requests':
            content = util.format_requests(cookies, args.domain)
        
        # 输出或保存
        if args.output:
            util.save_to_file(content, args.output)
        else:
            print(f"\n📋 {args.format.upper()} 格式输出:")
            print("-" * 50)
            print(content)
    
    except Exception as e:
        print(f"❌ 错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 