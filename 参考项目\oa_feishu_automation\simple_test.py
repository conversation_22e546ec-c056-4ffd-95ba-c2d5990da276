#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple test script for basic functionality validation
"""

import sys
import os
from pathlib import Path

# Add current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if all modules can be imported"""
    print("🧪 Testing module imports...")
    
    try:
        from config.settings import settings
        print("✅ Config settings imported")
        
        from core.cookie_manager import CookieManager
        print("✅ CookieManager imported")
        
        from core.oa_client import OAClient
        print("✅ OAClient imported")
        
        from core.excel_processor import ExcelProcessor
        print("✅ ExcelProcessor imported")
        
        from core.feishu_client import FeishuClient
        print("✅ FeishuClient imported")
        
        from utils.logger import setup_logger
        print("✅ Logger imported")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_configuration():
    """Test configuration loading"""
    print("\n⚙️  Testing configuration...")
    
    try:
        from config.settings import settings
        
        print(f"CookieCloud URL: {settings.cookiecloud.server_url}")
        print(f"Feishu Base ID: {settings.feishu.base_id}")
        print(f"OA Base URL: {settings.oa.base_url}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality without external dependencies"""
    print("\n🔧 Testing basic functionality...")
    
    try:
        from utils.date_utils import get_date_range, get_current_timestamp
        from utils.validators import clean_text, validate_amount
        
        # Test date utilities
        start_date, end_date = get_date_range(30)
        print(f"✅ Date range: {start_date} to {end_date}")
        
        # Test timestamp
        timestamp = get_current_timestamp()
        print(f"✅ Current timestamp: {timestamp}")
        
        # Test validators
        cleaned = clean_text("  test text  ")
        print(f"✅ Text cleaning: '{cleaned}'")
        
        amount_valid = validate_amount("123.45")
        print(f"✅ Amount validation: {amount_valid}")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality error: {e}")
        return False

def test_logger():
    """Test logger functionality"""
    print("\n📝 Testing logger...")
    
    try:
        from utils.logger import setup_logger
        
        logger = setup_logger(level="INFO")
        logger.info("Test log message")
        print("✅ Logger working")
        
        return True
        
    except Exception as e:
        print(f"❌ Logger error: {e}")
        return False

def test_directories():
    """Test directory creation"""
    print("\n📁 Testing directory creation...")
    
    try:
        # Create test directories
        test_dirs = ["downloads", "logs", "backups"]
        
        for dir_name in test_dirs:
            Path(dir_name).mkdir(exist_ok=True)
            if Path(dir_name).exists():
                print(f"✅ Directory created: {dir_name}")
            else:
                print(f"❌ Failed to create: {dir_name}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Directory creation error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Simple Test Script for OA to Feishu Automation")
    print("="*60)
    
    tests = [
        ("Module Imports", test_imports),
        ("Configuration", test_configuration),
        ("Basic Functionality", test_basic_functionality),
        ("Logger", test_logger),
        ("Directories", test_directories),
    ]
    
    results = {}
    all_passed = True
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running: {test_name}")
        print("="*60)
        
        try:
            result = test_func()
            results[test_name] = result
            if not result:
                all_passed = False
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results[test_name] = False
            all_passed = False
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 Test Results Summary")
    print("="*60)
    
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name:20} {status}")
    
    print("\n" + "="*60)
    if all_passed:
        print("🎉 All basic tests passed!")
        print("💡 Next step: Try running the main application")
        print("   python main.py --validate-only")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        print("📖 Refer to README.md for troubleshooting.")
    print("="*60)
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
