#!/usr/bin/env python3
import pandas as pd
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.feishu_client import FeishuClient
from config.settings import settings
from config.field_mappings import PREPAYMENT_FIELD_MAPPING
from utils.logger import setup_logger

def verify_data_completeness():
    """验证数据完整性"""
    logger = setup_logger()
    
    try:
        # 读取最新的Excel文件
        excel_file = "downloads/预付款申请单-20250604161902.xlsx"
        
        print("=== 正在分析数据完整性 ===")
        print(f"Excel文件: {excel_file}")
        
        # 读取Excel数据
        df = pd.read_excel(excel_file)
        print(f"Excel记录数: {len(df)}")
        print(f"Excel字段数: {len(df.columns)}")
        
        # 获取飞书数据
        feishu_client = FeishuClient()
        table_id = settings.feishu.payment_table_id
        
        # 获取飞书表格的记录
        records = feishu_client.get_table_records(table_id, page_size=100)
        print(f"飞书记录数: {len(records)}")
        
        # 获取字段信息
        fields = feishu_client.get_table_fields(table_id)
        feishu_field_names = [f.field_name for f in fields]
        print(f"飞书字段数: {len(feishu_field_names)}")
        
        print("\n=== 字段映射分析 ===")
        mapped_count = 0
        unmapped_excel_fields = []
        
        # 分析字段映射情况
        for excel_col in df.columns:
            if excel_col in PREPAYMENT_FIELD_MAPPING:
                feishu_field = PREPAYMENT_FIELD_MAPPING[excel_col]
                if feishu_field in feishu_field_names:
                    print(f"✅ '{excel_col}' -> '{feishu_field}'")
                    mapped_count += 1
                else:
                    print(f"❌ '{excel_col}' -> '{feishu_field}' (飞书中不存在)")
            else:
                unmapped_excel_fields.append(excel_col)
        
        print(f"\n📊 映射统计:")
        print(f"  - 已映射字段: {mapped_count}")
        print(f"  - 未映射Excel字段: {len(unmapped_excel_fields)}")
        print(f"  - 映射覆盖率: {mapped_count/len(df.columns)*100:.1f}%")
        
        if unmapped_excel_fields:
            print(f"\n📝 未映射的Excel字段:")
            for field in unmapped_excel_fields:
                print(f"   - {field}")
        
        # 检查数据样本
        if records:
            print(f"\n=== 飞书数据样本 (最新1条记录) ===")
            sample_record = list(records.values())[0]
            filled_fields = 0
            empty_fields = 0
            
            for field_name, field_value in sample_record.items():
                if field_value and str(field_value).strip():
                    filled_fields += 1
                    if len(str(field_value)) > 50:
                        display_value = str(field_value)[:50] + "..."
                    else:
                        display_value = field_value
                    print(f"  ✅ {field_name}: {display_value}")
                else:
                    empty_fields += 1
                    print(f"  ⭕ {field_name}: 空值")
            
            print(f"\n📊 数据填充统计:")
            print(f"  - 有数据字段: {filled_fields}")
            print(f"  - 空值字段: {empty_fields}")
            print(f"  - 数据填充率: {filled_fields/(filled_fields+empty_fields)*100:.1f}%")
        
        print(f"\n🎯 总结:")
        print(f"  - 数据传输: ✅ 成功 ({len(records)}条记录)")
        print(f"  - 字段映射: ✅ 良好 ({mapped_count}/{len(df.columns)}字段)")
        print(f"  - 数据质量: {'✅ 优秀' if filled_fields > empty_fields else '⚠️ 待优化'}")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")

if __name__ == "__main__":
    verify_data_completeness() 