#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mock Cookie Manager for testing without PyCookieCloud dependency
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from typing import Dict, List
from config.settings import settings
from utils.logger import setup_logger

class MockCookieManager:
    """
    Mock CookieManager for testing without PyCookieCloud
    """
    
    def __init__(self):
        """Initialize MockCookieManager"""
        self.logger = setup_logger()
        self.config = settings.cookiecloud
        self.all_cookies = None
        self.logger.info("MockCookieManager initialized (testing mode)")
    
    def load_cookies(self) -> bool:
        """
        Mock load cookies - returns test data
        
        Returns:
            True if cookies loaded successfully
        """
        try:
            self.logger.info("Loading mock cookies...")
            
            # Mock cookie data for testing
            self.all_cookies = {
                'oa.yaduo.com': [
                    {'name': 'ecology_JSessionid', 'value': 'test_session_id'},
                    {'name': 'loginidweaver', 'value': 'test_login_id'},
                    {'name': 'test_cookie', 'value': 'test_value'}
                ],
                'update_time': '2024-01-01 12:00:00'
            }
            
            self.logger.info(f"Successfully loaded mock cookies for {len(self.all_cookies)} domains")
            return True
            
        except Exception as e:
            self.logger.error(f"Error loading mock cookies: {e}")
            return False
    
    def get_domain_cookies(self, domain: str) -> List[Dict]:
        """
        Get mock cookies for a specific domain
        
        Args:
            domain: Target domain (e.g., 'oa.yaduo.com')
            
        Returns:
            List of cookie dictionaries for the domain
        """
        if not self.all_cookies:
            if not self.load_cookies():
                return []
        
        domain_cookies = []
        
        # Check exact domain match
        if domain in self.all_cookies:
            domain_cookies.extend(self.all_cookies[domain])
        
        self.logger.info(f"Found {len(domain_cookies)} mock cookies for domain {domain}")
        return domain_cookies
    
    def get_oa_cookies(self) -> Dict[str, str]:
        """
        Get mock OA system cookies in requests-compatible format
        
        Returns:
            Dictionary of cookie name-value pairs for OA domain
        """
        oa_domain = settings.oa.domain
        cookies = self.get_domain_cookies(oa_domain)
        
        cookie_dict = {}
        for cookie in cookies:
            name = cookie.get('name')
            value = cookie.get('value')
            if name and value:
                cookie_dict[name] = value
        
        self.logger.info(f"Prepared {len(cookie_dict)} mock cookies for OA authentication")
        return cookie_dict
    
    def get_cookie_header(self, domain: str) -> str:
        """
        Get mock cookies formatted as HTTP Cookie header
        
        Args:
            domain: Target domain
            
        Returns:
            Cookie header string
        """
        cookies = self.get_domain_cookies(domain)
        cookie_pairs = []
        
        for cookie in cookies:
            name = cookie.get('name')
            value = cookie.get('value')
            if name and value:
                cookie_pairs.append(f"{name}={value}")
        
        return '; '.join(cookie_pairs)
    
    def refresh_cookies(self) -> bool:
        """
        Mock refresh cookies
        
        Returns:
            True if refresh successful
        """
        self.logger.info("Refreshing mock cookies...")
        self.all_cookies = None
        return self.load_cookies()
    
    def validate_oa_cookies(self) -> bool:
        """
        Validate that required mock OA cookies are present
        
        Returns:
            True if required cookies are available
        """
        oa_cookies = self.get_oa_cookies()
        
        # Check for essential OA cookies
        required_cookies = ['ecology_JSessionid', 'loginidweaver']
        missing_cookies = [cookie for cookie in required_cookies if cookie not in oa_cookies]
        
        if missing_cookies:
            self.logger.warning(f"Missing required OA cookies: {missing_cookies}")
            return False
        
        self.logger.info("Mock OA cookies validation passed")
        return True
    
    def get_cookies_info(self) -> Dict:
        """
        Get information about available mock cookies
        
        Returns:
            Dictionary with cookie statistics
        """
        if not self.all_cookies:
            self.load_cookies()
        
        if not self.all_cookies:
            return {"total_domains": 0, "total_cookies": 0}
        
        total_cookies = 0
        domains = []
        
        for domain, cookies in self.all_cookies.items():
            if domain == 'update_time':
                continue
            if isinstance(cookies, list):
                total_cookies += len(cookies)
                domains.append(domain)
        
        return {
            "total_domains": len(domains),
            "total_cookies": total_cookies,
            "domains": domains,
            "update_time": self.all_cookies.get('update_time', 'Unknown')
        }
