# OA to Feishu Automation - 产品需求文档 (PRD)

## 📋 文档版本信息

| 版本 | 日期 | 作者 | 变更说明 |
|-----|------|------|----------|
| v1.0 | 2024-01-15 | 开发团队 | 初始版本 |
| v1.1 | 2024-01-20 | 开发团队 | 增加架构图和详细需求 |

---

## 1. 产品概述

### 1.1 产品背景

随着企业数字化转型的深入推进，传统OA系统与现代协作平台之间的数据孤岛问题日益突出。亚朵集团需要将OA系统中的报表数据实时同步到飞书多维表，以提高数据利用效率和决策响应速度。

### 1.2 产品定位

OA to Feishu Automation 是一个企业级数据集成自动化平台，专注于OA系统与飞书多维表之间的数据同步，提供高可靠性、高性能的数据传输服务。

### 1.3 核心价值

- **自动化操作**: 减少手动操作，提高数据同步效率
- **实时性保证**: 支持增量更新，确保数据实时性
- **高可靠性**: 完善的错误处理和重试机制
- **可扩展性**: 模块化设计，易于扩展新的数据源和目标

---

## 2. 业务需求分析

### 2.1 业务场景

```mermaid
graph TB
    A[OA系统报表] --> B[自动化同步平台]
    B --> C[飞书多维表]
    
    D[合同审批流程] --> A
    E[付款申请流程] --> A
    F[预付款申请流程] --> A
    
    C --> G[业务分析]
    C --> H[管理决策]
    C --> I[绩效监控]
    
    style A fill:#ff9999
    style B fill:#66b3ff
    style C fill:#99ff99
```

### 2.2 用户角色

| 角色 | 职责 | 需求描述 |
|------|------|----------|
| 财务人员 | 查看和分析财务数据 | 需要实时、准确的合同和付款数据 |
| 管理层 | 业务决策和监控 | 需要聚合分析和趋势数据 |
| 系统管理员 | 维护自动化系统 | 需要监控系统状态和处理异常 |
| 业务人员 | 使用飞书协作 | 需要便捷访问业务数据 |

### 2.3 业务流程

```mermaid
sequenceDiagram
    participant OA as OA系统
    participant Auto as 自动化平台
    participant Feishu as 飞书多维表
    participant User as 用户
    
    Note over Auto: 定时任务触发
    Auto->>OA: 获取认证Cookie
    Auto->>OA: 请求报表数据
    OA->>Auto: 返回Excel文件
    Auto->>Auto: 处理和验证数据
    Auto->>Feishu: 增量上传数据
    Feishu->>Auto: 确认上传结果
    Auto->>User: 发送通知(可选)
```

---

## 3. 功能需求

### 3.1 核心功能清单

| 功能模块 | 优先级 | 状态 | 描述 |
|----------|--------|------|------|
| 认证管理 | P0 | ✅ | CookieCloud集成，自动获取认证信息 |
| 数据采集 | P0 | ✅ | 从OA系统下载报表数据 |
| 数据处理 | P0 | ✅ | Excel文件解析和数据清洗 |
| 数据同步 | P0 | ✅ | 上传到飞书多维表 |
| 增量更新 | P1 | ✅ | 避免重复数据，支持增量同步 |
| 错误处理 | P1 | ✅ | 异常恢复和重试机制 |
| 日志监控 | P1 | ✅ | 详细的操作日志和状态监控 |
| 配置管理 | P2 | ✅ | 灵活的配置系统 |

### 3.2 支持的报表类型

```mermaid
graph LR
    A[OA报表类型] --> B[合同用印审批]
    A --> C[付款申请单]
    A --> D[预付款申请单]
    
    B --> E[Report ID: 142]
    C --> F[Report ID: 215]
    D --> G[Report ID: 216]
    
    E --> H[飞书表格: tbl3GijdiVYaJ4YK]
    F --> I[飞书表格: tbl4uI2rzYCjoTHu]
    G --> I
    
    style B fill:#ffd700
    style C fill:#87ceeb
    style D fill:#98fb98
```

### 3.3 数据映射规则

| OA字段 | 飞书字段 | 数据类型 | 验证规则 |
|--------|----------|----------|----------|
| 申请编号 | application_number | String | 必填，唯一标识 |
| 申请日期 | application_date | Date | 必填，格式验证 |
| 申请人 | applicant | String | 必填 |
| 申请部门 | department | String | 必填 |
| 金额 | amount | Number | 必填，正数 |
| 审批状态 | approval_status | Enum | 预定义状态值 |

---

## 4. 技术架构

### 4.1 系统架构图

```mermaid
graph TB
    subgraph "外部系统"
        OA[OA系统<br/>oa.yaduo.com]
        CC[CookieCloud<br/>认证服务]
        FS[飞书开放平台<br/>API服务]
    end
    
    subgraph "自动化平台"
        subgraph "接入层"
            CM[Cookie管理器]
            OAC[OA客户端]
            FSC[飞书客户端]
        end
        
        subgraph "处理层"
            EP[Excel处理器]
            DV[数据验证器]
            FM[字段映射器]
        end
        
        subgraph "控制层"
            MAIN[主控制器]
            CFG[配置管理]
            LOG[日志系统]
        end
        
        subgraph "存储层"
            CACHE[缓存存储]
            FILES[文件存储]
            LOGS[日志存储]
        end
    end
    
    CC --> CM
    CM --> OAC
    OAC --> OA
    OA --> EP
    EP --> DV
    DV --> FM
    FM --> FSC
    FSC --> FS
    
    MAIN --> OAC
    MAIN --> EP
    MAIN --> FSC
    CFG --> MAIN
    LOG --> LOGS
    
    style OA fill:#ff9999
    style FS fill:#99ff99
    style MAIN fill:#66b3ff
```

### 4.2 核心模块设计

```mermaid
classDiagram
    class CookieManager {
        +load_cookies()
        +validate_oa_cookies()
        +refresh_cookies()
        -parse_cookies()
    }
    
    class OAClient {
        +full_download_process()
        +export_report()
        +check_export_status()
        +download_file()
        -build_export_params()
    }
    
    class ExcelProcessor {
        +process_excel_file()
        +analyze_structure()
        +validate_records()
        -detect_header_row()
        -clean_data()
    }
    
    class FeishuClient {
        +get_access_token()
        +incremental_upload()
        +get_table_schema()
        +batch_update()
        -check_duplicates()
    }
    
    class MainController {
        +process_all_reports()
        +process_single_report()
        +validate_setup()
        -get_table_id_for_report()
    }
    
    MainController --> CookieManager
    MainController --> OAClient
    MainController --> ExcelProcessor
    MainController --> FeishuClient
    
    OAClient --> CookieManager
    ExcelProcessor --> FeishuClient
```

### 4.3 数据流架构

```mermaid
flowchart TD
    Start([开始]) --> Auth{认证检查}
    Auth -->|失败| RefreshAuth[刷新认证]
    RefreshAuth --> Auth
    Auth -->|成功| GetReports[获取报表列表]
    
    GetReports --> ProcessReport[处理单个报表]
    
    subgraph "报表处理流程"
        ProcessReport --> Download[下载Excel]
        Download --> Parse[解析数据]
        Parse --> Validate[数据验证]
        Validate --> Map[字段映射]
        Map --> CheckDup[检查重复]
        CheckDup -->|新数据| Insert[插入记录]
        CheckDup -->|重复| Update[更新记录]
        Insert --> NextReport{下一个报表?}
        Update --> NextReport
    end
    
    NextReport -->|是| ProcessReport
    NextReport -->|否| GenerateReport[生成处理报告]
    GenerateReport --> End([结束])
    
    style Download fill:#ffd700
    style Parse fill:#87ceeb
    style Validate fill:#98fb98
    style Map fill:#dda0dd
```

---

## 5. 技术实现详情

### 5.1 技术栈

| 层级 | 技术选型 | 版本 | 说明 |
|------|----------|------|------|
| 开发语言 | Python | 3.8+ | 主要开发语言 |
| HTTP客户端 | requests | 2.31+ | API调用和文件下载 |
| Excel处理 | pandas, openpyxl | latest | 数据处理和分析 |
| 配置管理 | python-dotenv | latest | 环境变量管理 |
| 日志系统 | logging | 内置 | 标准日志库 |
| 类型检查 | typing | 内置 | 类型注解支持 |

### 5.2 目录结构

```
oa_feishu_automation/
├── 📁 core/                    # 核心业务模块
│   ├── 🐍 cookie_manager.py    # CookieCloud集成
│   ├── 🐍 oa_client.py         # OA系统客户端
│   ├── 🐍 excel_processor.py   # Excel处理器
│   └── 🐍 feishu_client.py     # 飞书客户端
├── 📁 config/                  # 配置管理
│   ├── 🐍 settings.py          # 应用配置
│   └── 🐍 field_mappings.py    # 字段映射
├── 📁 utils/                   # 工具库
│   ├── 🐍 logger.py            # 日志系统
│   ├── 🐍 date_utils.py        # 日期工具
│   └── 🐍 validators.py        # 验证工具
├── 📁 downloads/               # 下载文件存储
├── 📁 logs/                    # 日志文件存储
├── 📁 backups/                 # 备份文件存储
├── 🐍 main.py                  # 主入口程序
├── 🐍 deploy.py                # 部署脚本
├── 🐍 test_setup.py            # 环境测试
└── 📄 requirements.txt         # 依赖包定义
```

### 5.3 关键算法设计

#### 5.3.1 增量更新算法

```mermaid
flowchart TD
    Input[输入新数据] --> GetExisting[获取现有数据]
    GetExisting --> BuildIndex[构建索引<br/>基于申请编号]
    BuildIndex --> Compare{比较记录}
    
    Compare -->|不存在| AddNew[添加新记录]
    Compare -->|已存在| CheckChange{检查变更}
    
    CheckChange -->|有变更| UpdateRecord[更新记录]
    CheckChange -->|无变更| Skip[跳过处理]
    
    AddNew --> Batch[批量操作]
    UpdateRecord --> Batch
    Skip --> Batch
    
    Batch --> Execute[执行上传]
    Execute --> Result[返回结果]
```

#### 5.3.2 错误重试机制

```mermaid
stateDiagram-v2
    [*] --> Executing
    Executing --> Success : 操作成功
    Executing --> Failed : 操作失败
    
    Failed --> CheckRetry : 检查重试次数
    CheckRetry --> Retry : 次数未超限
    CheckRetry --> GiveUp : 超过最大次数
    
    Retry --> Wait : 等待间隔
    Wait --> Executing : 重新执行
    
    Success --> [*]
    GiveUp --> [*]
```

---

## 6. 非功能性需求

### 6.1 性能要求

| 指标 | 要求 | 当前表现 |
|------|------|----------|
| 数据处理速度 | >1000条/分钟 | ~1500条/分钟 |
| API响应时间 | <5秒 | ~2秒 |
| 文件下载时间 | <30秒(10MB文件) | ~15秒 |
| 内存占用 | <512MB | ~200MB |
| 并发处理 | 支持3个报表并行 | 当前串行 |

### 6.2 可靠性要求

- **可用性**: 99.9%，支持24/7运行
- **容错性**: 网络异常自动重试，最多3次
- **数据一致性**: 强一致性，确保数据不丢失不重复
- **恢复能力**: 异常中断后可从断点继续

### 6.3 安全要求

- **认证安全**: Cookie自动更新，避免认证过期
- **数据传输**: HTTPS加密传输
- **敏感信息**: 环境变量存储，不在代码中硬编码
- **访问控制**: 最小权限原则

### 6.4 可维护性要求

- **代码质量**: 模块化设计，高内聚低耦合
- **文档完整**: 详细的代码注释和使用文档
- **日志记录**: 完整的操作日志，便于问题追踪
- **配置管理**: 配置与代码分离，支持环境切换

---

## 7. 部署和运维

### 7.1 部署架构

```mermaid
graph TB
    subgraph "生产环境"
        LB[负载均衡器]
        APP1[应用实例1]
        APP2[应用实例2]
        SCHED[定时调度器]
    end
    
    subgraph "监控体系"
        METRICS[指标收集]
        ALERT[告警系统]
        DASH[监控面板]
    end
    
    subgraph "存储系统"
        LOG_STORE[日志存储]
        FILE_STORE[文件存储]
        CONFIG_STORE[配置存储]
    end
    
    LB --> APP1
    LB --> APP2
    SCHED --> APP1
    SCHED --> APP2
    
    APP1 --> METRICS
    APP2 --> METRICS
    METRICS --> ALERT
    METRICS --> DASH
    
    APP1 --> LOG_STORE
    APP2 --> LOG_STORE
    APP1 --> FILE_STORE
    APP2 --> FILE_STORE
```

### 7.2 部署清单

```bash
# 1. 环境准备
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# 2. 配置文件
cp .env.example .env
# 编辑 .env 文件配置参数

# 3. 验证配置
python test_setup.py

# 4. 启动服务
python main.py

# 5. 定时任务 (crontab)
0 */2 * * * /path/to/venv/bin/python /path/to/main.py
```

### 7.3 监控指标

| 类别 | 指标名称 | 监控阈值 | 告警级别 |
|------|----------|----------|----------|
| 性能 | 处理耗时 | >300秒 | Warning |
| 性能 | 内存使用 | >80% | Warning |
| 可用性 | 成功率 | <95% | Critical |
| 可用性 | API错误率 | >5% | Warning |
| 业务 | 数据量异常 | 偏离均值>50% | Warning |
| 业务 | 重复数据率 | >10% | Warning |

---

## 8. 风险评估与应对

### 8.1 技术风险

| 风险点 | 影响程度 | 发生概率 | 应对措施 |
|--------|----------|----------|----------|
| OA系统API变更 | 高 | 中 | 版本兼容性检查，快速适配 |
| 飞书API限流 | 中 | 低 | 请求频率控制，重试机制 |
| Cookie过期 | 中 | 中 | 自动刷新机制，备用认证 |
| 网络异常 | 低 | 高 | 重试机制，超时控制 |

### 8.2 业务风险

| 风险点 | 影响程度 | 发生概率 | 应对措施 |
|--------|----------|----------|----------|
| 数据不一致 | 高 | 低 | 数据校验，定期对账 |
| 处理延迟 | 中 | 中 | 性能优化，并行处理 |
| 权限变更 | 中 | 低 | 权限监控，及时更新 |
| 数据泄露 | 高 | 极低 | 安全传输，访问控制 |

---

## 9. 测试策略

### 9.1 测试类型

```mermaid
graph TD
    A[测试策略] --> B[单元测试]
    A --> C[集成测试]
    A --> D[端到端测试]
    A --> E[性能测试]
    
    B --> B1[核心函数测试]
    B --> B2[数据处理测试]
    B --> B3[工具函数测试]
    
    C --> C1[API集成测试]
    C --> C2[数据流测试]
    C --> C3[错误处理测试]
    
    D --> D1[完整流程测试]
    D --> D2[多报表测试]
    D --> D3[异常恢复测试]
    
    E --> E1[压力测试]
    E --> E2[并发测试]
    E --> E3[长时间运行测试]
```

### 9.2 测试环境

| 环境 | 用途 | 数据源 | 配置 |
|------|------|--------|------|
| 开发环境 | 日常开发测试 | Mock数据 | 本地配置 |
| 测试环境 | 集成测试 | 测试数据 | 测试配置 |
| 预生产环境 | 上线前验证 | 生产副本 | 生产配置 |
| 生产环境 | 正式运行 | 真实数据 | 生产配置 |

---

## 10. 版本规划

### 10.1 已发布版本

| 版本 | 发布日期 | 主要功能 | 状态 |
|------|----------|----------|------|
| v1.0 | 2024-01-15 | 基础功能实现 | ✅ 已发布 |

### 10.2 后续版本规划

```mermaid
gantt
    title 版本发布计划
    dateFormat  YYYY-MM-DD
    section v1.1
    性能优化           :2024-02-01, 2024-02-15
    并行处理           :2024-02-10, 2024-02-25
    section v1.2
    监控告警           :2024-03-01, 2024-03-15
    Web界面           :2024-03-10, 2024-03-30
    section v2.0
    微服务架构         :2024-04-01, 2024-05-15
    多租户支持         :2024-05-01, 2024-06-15
```

### 10.3 功能路线图

| 版本 | 目标功能 | 预计时间 |
|------|----------|----------|
| v1.1 | 性能优化、并行处理 | 2024-02 |
| v1.2 | 监控告警、Web管理界面 | 2024-03 |
| v1.3 | 数据分析、报表生成 | 2024-04 |
| v2.0 | 微服务架构、多租户 | 2024-05 |

---

## 11. 附录

### 11.1 环境变量配置

```env
# CookieCloud配置
COOKIECLOUD_SERVER_URL=http://your-cookiecloud-server
COOKIECLOUD_UUID=your-uuid
COOKIECLOUD_PASSWORD=your-password

# 飞书配置
FEISHU_APP_ID=cli_xxxxxxxxxxxxxxxx
FEISHU_APP_SECRET=your-app-secret
FEISHU_BASE_ID=PMatbmyEkae3YCsqPJIcOL0AnBh
FEISHU_CONTRACT_TABLE_ID=tbl3GijdiVYaJ4YK
FEISHU_PAYMENT_TABLE_ID=tbl4uI2rzYCjoTHu

# OA系统配置
OA_BASE_URL=http://oa.yaduo.com
OA_DOMAIN=oa.yaduo.com

# 应用配置
LOG_LEVEL=INFO
DATE_RANGE_DAYS=30
TIMEOUT=30
MAX_RETRIES=3
RETRY_INTERVAL=2
```

### 11.2 API接口文档

详细的API接口文档请参考：
- [OA系统API文档](docs/oa_api.md)
- [飞书开放平台API文档](https://open.feishu.cn/document/)

### 11.3 故障排查指南

常见问题和解决方案请参考：
- [故障排查指南](docs/troubleshooting.md)
- [FAQ常见问题](docs/faq.md)

---

**文档维护**: 开发团队  
**最后更新**: 2024-01-20  
**审核状态**: 已审核
