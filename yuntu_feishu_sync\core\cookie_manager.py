#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie管理器 - 基于CookieCloud
"""

import json
from typing import Dict, List, Optional, Any
from PyCookieCloud import PyCookieCloud

from ..config import settings
from ..utils import setup_logger

class CookieManager:
    """
    Cookie管理器 - 使用CookieCloud获取和管理cookie
    """
    
    def __init__(self):
        """初始化Cookie管理器"""
        self.logger = setup_logger()
        self.config = settings.cookiecloud
        self.client = None
        self.all_cookies = None
    
    def _get_client(self) -> PyCookieCloud:
        """获取CookieCloud客户端"""
        if self.client is None:
            self.client = PyCookieCloud(
                self.config.server_url,
                self.config.uuid,
                self.config.password
            )
        return self.client
    
    def load_cookies(self) -> Dict[str, Any]:
        """
        从CookieCloud加载所有cookie数据
        
        Returns:
            cookie数据字典
        """
        if self.all_cookies is None:
            self.logger.info("正在从CookieCloud获取cookie数据...")
            client = self._get_client()
            self.all_cookies = client.get_decrypted_data()
            
            if not self.all_cookies:
                raise Exception("获取cookie数据失败")
            
            self.logger.info("成功获取cookie数据")
        
        return self.all_cookies
    
    def get_domain_cookies(self, domain: str) -> List[Dict[str, str]]:
        """
        获取指定域名的cookie
        
        Args:
            domain: 域名
            
        Returns:
            cookie列表
        """
        cookies = self.load_cookies()
        domain_cookies = cookies.get(domain, [])
        
        if not domain_cookies:
            self.logger.warning(f"未找到域名 {domain} 的cookie")
            return []
        
        self.logger.info(f"获取到域名 {domain} 的 {len(domain_cookies)} 个cookie")
        return domain_cookies
    
    def get_cookies_for_requests(self, domain: str) -> Dict[str, str]:
        """
        获取用于requests的cookie字典
        
        Args:
            domain: 域名
            
        Returns:
            cookie字典
        """
        domain_cookies = self.get_domain_cookies(domain)
        
        cookie_dict = {}
        for cookie in domain_cookies:
            if isinstance(cookie, dict) and 'name' in cookie and 'value' in cookie:
                cookie_dict[cookie['name']] = cookie['value']
        
        self.logger.info(f"转换为requests格式的cookie: {len(cookie_dict)} 个")
        return cookie_dict
    
    def get_cookie_header(self, domain: str) -> str:
        """
        获取Cookie请求头字符串
        
        Args:
            domain: 域名
            
        Returns:
            Cookie头字符串
        """
        domain_cookies = self.get_domain_cookies(domain)
        
        cookie_pairs = []
        for cookie in domain_cookies:
            if isinstance(cookie, dict) and 'name' in cookie and 'value' in cookie:
                cookie_pairs.append(f"{cookie['name']}={cookie['value']}")
        
        cookie_header = "; ".join(cookie_pairs)
        self.logger.info(f"生成Cookie头，长度: {len(cookie_header)} 字符")
        return cookie_header
    
    def save_cookies_to_file(self, file_path: str, domain: Optional[str] = None):
        """
        保存cookie到文件
        
        Args:
            file_path: 保存路径
            domain: 指定域名，None表示保存所有
        """
        if domain:
            cookies_to_save = {domain: self.get_domain_cookies(domain)}
        else:
            cookies_to_save = self.load_cookies()
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(cookies_to_save, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"Cookie已保存到: {file_path}")
    
    def test_connection(self) -> bool:
        """
        测试CookieCloud连接
        
        Returns:
            连接是否成功
        """
        try:
            self.logger.info("测试CookieCloud连接...")
            cookies = self.load_cookies()
            
            if cookies:
                total_cookies = 0
                domains = []
                
                for domain, cookie_list in cookies.items():
                    if domain == 'update_time':
                        continue
                    if isinstance(cookie_list, list):
                        total_cookies += len(cookie_list)
                        domains.append((domain, len(cookie_list)))
                
                self.logger.info(f"连接成功! 域名数量: {len(domains)}, Cookie总数: {total_cookies}")
                
                if 'update_time' in cookies:
                    self.logger.info(f"更新时间: {cookies['update_time']}")
                
                return True
            else:
                self.logger.error("连接失败: 未获取到数据")
                return False
                
        except Exception as e:
            self.logger.error(f"连接测试失败: {e}")
            return False
