#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步管理器 - 协调云图数据同步到飞书
"""

from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from .core import FeishuClient, YuntuClient, DataProcessor
from .config import settings
from .utils import setup_logger

class SyncManager:
    """
    同步管理器 - 协调整个同步流程
    """
    
    def __init__(self):
        """初始化同步管理器"""
        self.logger = setup_logger()
        self.feishu_client = FeishuClient()
        self.yuntu_client = YuntuClient()
        self.data_processor = DataProcessor()
    
    def sync_historical_data(self, start_year: int = 2024, start_month: int = 1,
                            max_months: int = 24) -> Dict[str, Any]:
        """
        同步历史数据

        Args:
            start_year: 开始年份
            start_month: 开始月份
            max_months: 最多同步多少个月

        Returns:
            同步结果统计
        """
        try:
            self.logger.info(f"开始同步历史数据: {start_year}/{start_month:02d} 开始，最多 {max_months} 个月")

            # 步骤1: 获取历史数据
            self.logger.info("步骤1: 获取云图历史数据")
            historical_data = self.yuntu_client.get_historical_data(start_year, start_month, max_months)

            if not historical_data:
                self.logger.warning("没有获取到历史数据")
                return {"success": True, "message": "没有历史数据", "added": 0, "updated": 0}

            # 步骤2: 处理数据
            self.logger.info("步骤2: 处理历史数据")
            all_records = self.data_processor.process_historical_data(historical_data)

            if not all_records:
                self.logger.warning("没有数据需要同步")
                return {"success": True, "message": "没有数据需要同步", "added": 0, "updated": 0}

            # 验证和去重
            all_records = self.data_processor.validate_records(all_records)
            all_records = self.data_processor.deduplicate_records(all_records)

            # 步骤3: 增量同步到飞书
            self.logger.info("步骤3: 增量同步数据到飞书多维表")
            sync_result = self._incremental_sync_to_feishu(all_records)

            # 返回结果
            result = {
                "success": True,
                "months_processed": len(historical_data),
                "total_records": len(all_records),
                **sync_result
            }

            self.logger.info(f"历史数据同步完成: {result}")
            return result

        except Exception as e:
            self.logger.error(f"历史数据同步过程中出错: {e}")
            return {"success": False, "error": str(e)}

    def sync_latest_data(self) -> Dict[str, Any]:
        """
        同步最新数据（当前月或上个月）

        Returns:
            同步结果统计
        """
        try:
            self.logger.info("开始同步最新数据")

            # 步骤1: 获取最新数据
            self.logger.info("步骤1: 获取云图最新数据")
            result = self.yuntu_client.get_latest_month_data()

            if not result:
                self.logger.warning("没有获取到最新数据")
                return {"success": True, "message": "没有最新数据", "added": 0, "updated": 0}

            data_period, yuntu_data = result

            # 步骤2: 处理数据
            self.logger.info(f"步骤2: 处理 {data_period} 的数据")
            records = self.data_processor.process_yuntu_data(yuntu_data, data_period)

            if not records:
                self.logger.warning("没有数据需要同步")
                return {"success": True, "message": "没有数据需要同步", "added": 0, "updated": 0}

            # 验证和去重
            records = self.data_processor.validate_records(records)
            records = self.data_processor.deduplicate_records(records)

            # 步骤3: 增量同步到飞书
            self.logger.info("步骤3: 增量同步数据到飞书多维表")
            sync_result = self._incremental_sync_to_feishu(records)

            # 返回结果
            result = {
                "success": True,
                "data_period": data_period,
                "total_records": len(records),
                **sync_result
            }

            self.logger.info(f"最新数据同步完成: {result}")
            return result

        except Exception as e:
            self.logger.error(f"最新数据同步过程中出错: {e}")
            return {"success": False, "error": str(e)}
    
    def _incremental_sync_to_feishu(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        增量同步数据到飞书（参考质检报告项目的逻辑）

        Args:
            records: 要同步的记录

        Returns:
            同步结果
        """
        table_id = settings.feishu.table_id

        # 获取现有记录，使用触点名称和数据周期作为键
        self.logger.info("获取飞书现有记录...")
        existing_records = self.feishu_client.get_table_records(table_id)

        # 构建现有记录的查找字典
        existing_lookup = {}
        for record_id, fields in existing_records.items():
            trigger_name = fields.get('触点', '')
            data_period = fields.get('数据周期', '')
            if trigger_name and data_period:
                key = f"{trigger_name}_{data_period}"
                existing_lookup[key] = (record_id, fields)

        self.logger.info(f"现有记录数量: {len(existing_lookup)}")

        # 分类新记录
        records_to_add = []
        records_to_update = []
        skipped_count = 0

        for record in records:
            unique_key = self.data_processor.create_unique_key(record)

            if unique_key in existing_lookup:
                # 记录已存在，检查是否需要更新
                existing_record_id, existing_fields = existing_lookup[unique_key]

                if self._needs_update(record, existing_fields):
                    records_to_update.append((existing_record_id, record))
                    self.logger.debug(f"记录需要更新: {unique_key}")
                else:
                    skipped_count += 1
                    self.logger.debug(f"记录无变化，跳过: {unique_key}")
            else:
                # 新记录
                records_to_add.append(record)
                self.logger.debug(f"新记录: {unique_key}")

        self.logger.info(f"分类完成: {len(records_to_add)} 条新增, {len(records_to_update)} 条更新, {skipped_count} 条跳过")

        result = {"added": 0, "updated": 0, "skipped": skipped_count, "failed": 0}

        # 批量添加新记录
        if records_to_add:
            self.logger.info(f"批量添加 {len(records_to_add)} 条新记录...")
            success, added_count, failed_records = self.feishu_client.batch_add_records(
                table_id, records_to_add
            )
            result["added"] = added_count
            result["failed"] += len(failed_records)

        # 批量更新现有记录
        if records_to_update:
            self.logger.info(f"批量更新 {len(records_to_update)} 条记录...")
            updated_count = 0
            for record_id, new_fields in records_to_update:
                if self._update_single_record(table_id, record_id, new_fields):
                    updated_count += 1
                else:
                    result["failed"] += 1
            result["updated"] = updated_count

        return result

    def _needs_update(self, new_record: Dict[str, Any], existing_fields: Dict[str, Any]) -> bool:
        """
        检查记录是否需要更新

        Args:
            new_record: 新记录
            existing_fields: 现有记录的字段

        Returns:
            是否需要更新
        """
        # 比较数值字段
        number_fields = ['转化UV', '曝光转化率', '新客占比', '客单价',
                        '参考转化UV', '参考曝光转化率', '参考新客占比', '参考客单价']

        for field in number_fields:
            new_value = new_record.get(field)
            existing_value = existing_fields.get(field)

            # 数值比较，考虑精度问题
            if new_value is not None and existing_value is not None:
                if abs(float(new_value) - float(existing_value)) > 0.001:  # 精度阈值
                    return True
            elif new_value != existing_value:
                return True

        return False
    
    def _update_single_record(self, table_id: str, record_id: str, fields: Dict[str, Any]) -> bool:
        """
        更新单条记录
        
        Args:
            table_id: 表格ID
            record_id: 记录ID
            fields: 字段数据
            
        Returns:
            是否成功
        """
        endpoint = f"bitable/v1/apps/{settings.feishu.base_id}/tables/{table_id}/records/{record_id}"
        data = {'fields': fields}
        
        result = self.feishu_client._make_api_request('PUT', endpoint, data)
        if result:
            self.logger.debug(f"成功更新记录 {record_id}")
            return True
        else:
            self.logger.error(f"更新记录 {record_id} 失败")
            return False
    
    def test_all_connections(self) -> Dict[str, bool]:
        """
        测试所有连接
        
        Returns:
            连接测试结果
        """
        self.logger.info("开始测试所有连接...")
        
        results = {}
        
        # 测试CookieCloud连接
        try:
            results["cookiecloud"] = self.yuntu_client.cookie_manager.test_connection()
        except Exception as e:
            self.logger.error(f"CookieCloud连接测试失败: {e}")
            results["cookiecloud"] = False
        
        # 测试云图API连接
        try:
            results["yuntu_api"] = self.yuntu_client.test_connection()
        except Exception as e:
            self.logger.error(f"云图API连接测试失败: {e}")
            results["yuntu_api"] = False
        
        # 测试飞书API连接
        try:
            token = self.feishu_client.get_access_token()
            results["feishu_api"] = token is not None
        except Exception as e:
            self.logger.error(f"飞书API连接测试失败: {e}")
            results["feishu_api"] = False
        
        self.logger.info(f"连接测试结果: {results}")
        return results
    
    def get_table_schema(self) -> Dict[str, Any]:
        """
        获取飞书表格结构
        
        Returns:
            表格结构信息
        """
        return self.feishu_client.get_table_schema(settings.feishu.table_id)
    
    def save_table_schema(self, file_path: Optional[str] = None) -> str:
        """
        保存表格结构到文件
        
        Args:
            file_path: 保存路径
            
        Returns:
            保存的文件路径
        """
        return self.feishu_client.save_schema_to_file(settings.feishu.table_id, file_path)
