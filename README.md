# 云图数据同步到飞书多维表工具

这是一个用于将云图(yuntu.oceanengine.com)的触点效果分析数据自动同步到飞书多维表的工具。

## 功能特性

- 🔄 **智能数据同步**: 从云图API获取触点效果分析数据并同步到飞书多维表
- 🍪 **Cookie管理**: 使用CookieCloud自动获取和管理登录状态
- 📊 **增量更新**: 基于触点名称和数据周期的智能增量更新，避免重复数据
- � **月份数据处理**: 正确处理云图的月份数据格式（如2025/05）
- 🏛️ **历史数据同步**: 支持批量同步历史数据，可指定起始年月
- �🔍 **表格结构分析**: 自动读取和分析飞书表格结构，确保字段匹配
- 🧪 **连接测试**: 全面的连接和配置测试功能
- 📝 **详细日志**: 完整的操作日志记录和错误处理

## 项目结构

```
云图by月转化效能数据同步/
├── yuntu_feishu_sync/          # 主要代码包
│   ├── config/                 # 配置模块
│   │   ├── __init__.py
│   │   └── settings.py
│   ├── core/                   # 核心功能模块
│   │   ├── __init__.py
│   │   ├── feishu_client.py    # 飞书API客户端
│   │   ├── cookie_manager.py   # Cookie管理器
│   │   ├── yuntu_client.py     # 云图API客户端
│   │   └── data_processor.py   # 数据处理器
│   ├── utils/                  # 工具模块
│   │   ├── __init__.py
│   │   └── logger.py
│   ├── __init__.py
│   └── sync_manager.py         # 同步管理器
├── tools/                      # 工具脚本
│   ├── read_table_schema.py    # 表格结构读取工具
│   └── test_connections.py     # 连接测试工具
├── schemas/                    # 表格结构文件 (自动生成)
├── logs/                       # 日志文件 (自动生成)
├── main.py                     # 主程序入口
├── requirements.txt            # 依赖包列表
├── .env.example               # 环境配置模板
└── README.md                  # 说明文档
```

## 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

复制 `.env.example` 为 `.env` 并填入实际配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
# CookieCloud 配置
COOKIECLOUD_SERVER_URL=https://your-cookiecloud-server.com
COOKIECLOUD_UUID=your-uuid-here
COOKIECLOUD_PASSWORD=your-password-here

# 飞书应用配置
FEISHU_APP_ID=your-feishu-app-id
FEISHU_APP_SECRET=your-feishu-app-secret
FEISHU_BASE_ID=LLsAbYzcaaRF6ws6e05c74STnee
FEISHU_TABLE_ID=tblrpauvighksPhI
```

### 3. 配置说明

#### CookieCloud配置
- 需要先部署CookieCloud服务
- 在浏览器中安装CookieCloud插件
- 确保已登录云图网站并同步cookie

#### 飞书应用配置
- 在飞书开放平台创建应用
- 获取App ID和App Secret
- 确保应用有多维表的读写权限

## 使用方法

### 1. 测试连接

首先测试所有连接是否正常：

```bash
python main.py --test
```

### 2. 读取表格结构

分析飞书多维表的结构：

```bash
python main.py --schema
```

或使用专门的工具：

```bash
python tools/read_table_schema.py
```

### 3. 数据同步

同步昨天的数据（默认）：

```bash
python main.py
```

同步指定日期的数据：

```bash
python main.py --date 2025-06-08
```

同步日期范围的数据：

```bash
python main.py --start 2025-06-01 --end 2025-06-07
```

### 4. 详细输出

使用 `-v` 参数获取详细日志：

```bash
python main.py -v --test
```

## 数据字段映射

工具会将云图的触点效果分析数据映射到飞书多维表的以下字段：

| 云图字段 | 飞书字段 | 数据类型 | 说明 |
|---------|---------|---------|------|
| trigger_point_id | 触点ID | 文本 | 触点唯一标识 |
| trigger_point_name | 触点名称 | 文本 | 触点显示名称 |
| convert_uv | 转化UV | 数字 | 转化用户数 |
| convert_rate | 转化率 | 数字 | 转化率 |
| convert_new_rate | 新客转化率 | 数字 | 新客户转化率 |
| average_transaction_value | 平均交易价值 | 数字 | 平均交易金额 |
| bm_convert_uv | 基准转化UV | 数字 | 基准转化用户数 |
| bm_convert_rate | 基准转化率 | 数字 | 基准转化率 |
| bm_convert_new_rate | 基准新客转化率 | 数字 | 基准新客户转化率 |
| bm_average_transaction_value | 基准平均交易价值 | 数字 | 基准平均交易金额 |
| data_date | 数据日期 | 日期 | 数据对应的日期 |
| sync_time | 同步时间 | 日期时间 | 数据同步的时间 |
| advertiser_id | 广告主ID | 文本 | 广告主标识 |

## 故障排查

### 1. CookieCloud连接失败
- 检查CookieCloud服务是否正常运行
- 确认UUID和密码是否正确
- 检查网络连接

### 2. 云图API请求失败
- 确认已在浏览器中登录云图网站
- 检查CookieCloud是否已同步最新cookie
- 确认广告主ID是否正确

### 3. 飞书API请求失败
- 检查App ID和App Secret是否正确
- 确认应用权限是否足够
- 检查Base ID和Table ID是否正确

### 4. 数据同步失败
- 检查表格字段是否与配置匹配
- 确认数据格式是否正确
- 查看详细日志了解具体错误

## 日志文件

程序会在 `logs/` 目录下生成日志文件：
- 文件名格式：`yuntu_sync_YYYYMMDD.log`
- 包含详细的操作记录和错误信息

## 注意事项

1. **数据安全**: 请妥善保管CookieCloud和飞书的配置信息
2. **频率限制**: 注意API调用频率，避免触发限制
3. **数据一致性**: 建议定期检查同步的数据是否正确
4. **备份**: 重要数据请做好备份

## 技术架构

- **语言**: Python 3.8+
- **主要依赖**: requests, python-dotenv, PyCookieCloud
- **设计模式**: 模块化设计，职责分离
- **错误处理**: 完善的异常处理和重试机制
- **日志系统**: 分级日志记录

## 贡献

欢迎提交Issue和Pull Request来改进这个工具。

## 许可证

MIT License
