#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书客户端 - 基于参考项目优化
"""

import requests
import time
import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from pathlib import Path

from ..config import settings
from ..utils import setup_logger

@dataclass
class FeishuTableField:
    """飞书表格字段信息"""
    field_id: str
    field_name: str
    field_type: int
    description: Optional[str] = None

class FeishuClient:
    """
    飞书客户端 - 用于多维表操作
    """
    
    def __init__(self):
        """初始化飞书客户端"""
        self.logger = setup_logger()
        self.config = settings.feishu
        self.access_token = None
        self.token_expires_at = 0
        self.session = requests.Session()
    
    def get_access_token(self) -> Optional[str]:
        """
        获取tenant access token
        
        Returns:
            访问令牌，失败返回None
        """
        # 检查当前token是否仍然有效
        if self.access_token and time.time() < self.token_expires_at:
            return self.access_token
        
        url = self.config.auth_url
        headers = {
            'Content-Type': 'application/json; charset=utf-8'
        }
        
        data = {
            'app_id': self.config.app_id,
            'app_secret': self.config.app_secret
        }
        
        try:
            self.logger.info("正在请求飞书访问令牌...")
            response = self.session.post(url, json=data, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    self.access_token = result['tenant_access_token']
                    # Token有效期2小时，提前10分钟刷新
                    self.token_expires_at = time.time() + result.get('expire', 7200) - 600
                    self.logger.info("成功获取飞书访问令牌")
                    return self.access_token
                else:
                    self.logger.error(f"获取访问令牌失败: {result}")
                    return None
            else:
                self.logger.error(f"HTTP错误 {response.status_code}: {response.text}")
                return None
                
        except requests.RequestException as e:
            self.logger.error(f"网络错误: {e}")
            return None
    
    def _make_api_request(self, method: str, endpoint: str, data: Optional[Dict] = None, 
                         params: Optional[Dict] = None) -> Optional[Dict]:
        """
        发起API请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            data: 请求体数据
            params: 查询参数
            
        Returns:
            响应数据，失败返回None
        """
        access_token = self.get_access_token()
        if not access_token:
            self.logger.error("无法获取访问令牌")
            return None
        
        url = f"{self.config.base_url}{endpoint}"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json; charset=utf-8'
        }
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, headers=headers, params=params, timeout=30)
            elif method.upper() == 'POST':
                response = self.session.post(url, headers=headers, json=data, params=params, timeout=30)
            elif method.upper() == 'PUT':
                response = self.session.put(url, headers=headers, json=data, params=params, timeout=30)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url, headers=headers, params=params, timeout=30)
            else:
                self.logger.error(f"不支持的HTTP方法: {method}")
                return None
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    return result.get('data', {})
                else:
                    self.logger.error(f"API错误: {result}")
                    return None
            else:
                self.logger.error(f"HTTP错误 {response.status_code}: {response.text}")
                return None
                
        except requests.RequestException as e:
            self.logger.error(f"网络错误: {e}")
            return None
    
    def get_table_fields(self, table_id: str) -> List[FeishuTableField]:
        """
        获取表格字段定义
        
        Args:
            table_id: 表格ID
            
        Returns:
            字段列表
        """
        endpoint = f"bitable/v1/apps/{self.config.base_id}/tables/{table_id}/fields"
        
        result = self._make_api_request('GET', endpoint)
        if not result:
            return []
        
        fields = []
        for item in result.get('items', []):
            field = FeishuTableField(
                field_id=item['field_id'],
                field_name=item['field_name'],
                field_type=item['type'],
                description=item.get('description')
            )
            fields.append(field)
        
        self.logger.info(f"获取到 {len(fields)} 个字段")
        return fields
    
    def get_table_schema(self, table_id: str) -> Dict[str, Any]:
        """
        获取完整的表格结构信息
        
        Args:
            table_id: 表格ID
            
        Returns:
            表格结构信息
        """
        # 获取表格信息
        table_endpoint = f"bitable/v1/apps/{self.config.base_id}/tables/{table_id}"
        table_info = self._make_api_request('GET', table_endpoint)
        
        # 获取字段定义
        fields = self.get_table_fields(table_id)
        
        schema = {
            "table_info": table_info,
            "fields": [
                {
                    "field_id": field.field_id,
                    "field_name": field.field_name,
                    "field_type": field.field_type,
                    "description": field.description
                }
                for field in fields
            ]
        }
        
        self.logger.info(f"获取表格 {table_id} 的结构信息: {len(fields)} 个字段")
        return schema
    
    def save_schema_to_file(self, table_id: str, file_path: Optional[str] = None) -> str:
        """
        保存表格结构到文件
        
        Args:
            table_id: 表格ID
            file_path: 保存路径，默认为schemas目录
            
        Returns:
            保存的文件路径
        """
        schema = self.get_table_schema(table_id)
        
        if not file_path:
            # 创建schemas目录
            schemas_dir = Path("schemas")
            schemas_dir.mkdir(exist_ok=True)
            file_path = schemas_dir / f"table_{table_id}_schema.json"
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(schema, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"表格结构已保存到: {file_path}")
        return str(file_path)

    def get_table_records(self, table_id: str, field_names: Optional[List[str]] = None,
                         page_size: int = 500) -> Dict[str, Dict]:
        """
        获取表格现有记录

        Args:
            table_id: 表格ID
            field_names: 指定字段名称
            page_size: 每页记录数

        Returns:
            记录字典，key为record_id
        """
        endpoint = f"bitable/v1/apps/{self.config.base_id}/tables/{table_id}/records"

        params: Dict[str, Any] = {
            'page_size': page_size
        }

        if field_names:
            params['field_names'] = ','.join(field_names)

        all_records = {}
        page_token = None

        while True:
            if page_token:
                params['page_token'] = page_token

            result = self._make_api_request('GET', endpoint, params=params)
            if not result:
                break

            # 处理记录
            for item in result.get('items', []):
                record_id = item['record_id']
                all_records[record_id] = item['fields']

            # 检查是否有下一页
            page_token = result.get('page_token')
            if not result.get('has_more', False):
                break

        self.logger.info(f"获取到 {len(all_records)} 条现有记录")
        return all_records

    def batch_add_records(self, table_id: str, records: List[Dict[str, Any]],
                         batch_size: int = 500) -> Tuple[bool, int, List[str]]:
        """
        批量添加记录

        Args:
            table_id: 表格ID
            records: 记录列表
            batch_size: 批次大小

        Returns:
            (成功标志, 添加数量, 失败记录)
        """
        if not records:
            self.logger.info("没有记录需要添加")
            return True, 0, []

        endpoint = f"bitable/v1/apps/{self.config.base_id}/tables/{table_id}/records/batch_create"

        total_added = 0
        failed_records = []

        # 分批处理
        for i in range(0, len(records), batch_size):
            batch = records[i:i + batch_size]

            # 格式化记录
            formatted_records = []
            for record in batch:
                formatted_records.append({'fields': record})

            data = {'records': formatted_records}

            result = self._make_api_request('POST', endpoint, data)
            if result:
                added_count = len(result.get('records', []))
                total_added += added_count
                self.logger.info(f"批次 {i//batch_size + 1}: 添加了 {added_count} 条记录")
            else:
                self.logger.error(f"批次 {i//batch_size + 1} 添加失败")
                failed_records.extend([f"batch_{i//batch_size + 1}_record_{j}" for j in range(len(batch))])

        success = len(failed_records) == 0
        self.logger.info(f"批量添加完成: {total_added} 条成功, {len(failed_records)} 条失败")
        return success, total_added, failed_records
