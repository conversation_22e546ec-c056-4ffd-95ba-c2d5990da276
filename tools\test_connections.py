#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连接测试工具
测试CookieCloud、云图API和飞书API的连接状态
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from yuntu_feishu_sync import SyncManager
from yuntu_feishu_sync.utils import setup_logger

def test_cookiecloud():
    """测试CookieCloud连接"""
    print("🍪 测试CookieCloud连接...")
    try:
        from yuntu_feishu_sync.core import CookieManager
        cookie_manager = CookieManager()
        
        success = cookie_manager.test_connection()
        if success:
            print("✅ CookieCloud连接成功")
            
            # 测试获取云图域名的cookie
            yuntu_cookies = cookie_manager.get_domain_cookies("yuntu.oceanengine.com")
            if yuntu_cookies:
                print(f"   📊 获取到云图域名cookie: {len(yuntu_cookies)} 个")
                
                # 显示部分cookie信息
                print("   🔍 部分cookie信息:")
                for i, cookie in enumerate(yuntu_cookies[:3]):
                    if isinstance(cookie, dict):
                        name = cookie.get('name', 'unknown')
                        value = cookie.get('value', '')
                        print(f"      {i+1}. {name}: {value[:20]}...")
                    
            else:
                print("   ⚠️  未获取到云图域名的cookie")
        else:
            print("❌ CookieCloud连接失败")
        
        return success
        
    except Exception as e:
        print(f"❌ CookieCloud测试出错: {e}")
        return False

def test_yuntu_api():
    """测试云图API连接"""
    print("\n🌐 测试云图API连接...")
    try:
        from yuntu_feishu_sync.core import YuntuClient
        yuntu_client = YuntuClient()
        
        success = yuntu_client.test_connection()
        if success:
            print("✅ 云图API连接成功")
        else:
            print("❌ 云图API连接失败")
        
        return success
        
    except Exception as e:
        print(f"❌ 云图API测试出错: {e}")
        return False

def test_feishu_api():
    """测试飞书API连接"""
    print("\n📱 测试飞书API连接...")
    try:
        from yuntu_feishu_sync.core import FeishuClient
        from yuntu_feishu_sync.config import settings
        
        feishu_client = FeishuClient()
        
        # 测试获取访问令牌
        token = feishu_client.get_access_token()
        if token:
            print("✅ 飞书API连接成功")
            print(f"   🔑 访问令牌: {token[:20]}...")
            
            # 测试获取表格信息
            try:
                fields = feishu_client.get_table_fields(settings.feishu.table_id)
                if fields:
                    print(f"   📊 表格字段数量: {len(fields)}")
                    print("   📝 前3个字段:")
                    for i, field in enumerate(fields[:3]):
                        print(f"      {i+1}. {field.field_name} (类型: {field.field_type})")
                else:
                    print("   ⚠️  未获取到表格字段")
            except Exception as e:
                print(f"   ⚠️  获取表格信息失败: {e}")
            
            return True
        else:
            print("❌ 飞书API连接失败")
            return False
        
    except Exception as e:
        print(f"❌ 飞书API测试出错: {e}")
        return False

def test_data_flow():
    """测试数据流程"""
    print("\n🔄 测试数据流程...")
    try:
        from yuntu_feishu_sync.core import YuntuClient, DataProcessor
        from datetime import datetime, timedelta
        
        # 获取昨天的数据
        yuntu_client = YuntuClient()
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        print(f"   📅 获取 {yesterday} 的数据...")
        yuntu_data = yuntu_client.get_trigger_insight_data(yesterday, yesterday)
        
        if yuntu_data:
            distribution_list = yuntu_data.get('distribution_list', [])
            print(f"   📊 获取到 {len(distribution_list)} 个触点数据")
            
            if distribution_list:
                # 显示第一个触点的信息
                first_item = distribution_list[0]
                trigger_point = first_item.get('trigger_point', {})
                print(f"   🎯 第一个触点: {trigger_point.get('trigger_point_name', '未知')}")
                
                # 测试数据处理
                data_processor = DataProcessor()
                records = data_processor.process_yuntu_data(yuntu_data, yesterday)
                
                if records:
                    print(f"   ✅ 数据处理成功: {len(records)} 条记录")
                    
                    # 显示第一条记录的部分信息
                    first_record = records[0]
                    print("   📝 第一条记录示例:")
                    for key, value in list(first_record.items())[:5]:
                        print(f"      {key}: {value}")
                    
                    return True
                else:
                    print("   ❌ 数据处理失败")
                    return False
            else:
                print("   ⚠️  没有触点数据")
                return False
        else:
            print("   ❌ 获取云图数据失败")
            return False
        
    except Exception as e:
        print(f"❌ 数据流程测试出错: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logger()
    
    print("🧪 云图飞书同步工具 - 连接测试")
    print("=" * 50)
    
    # 显示配置信息
    from yuntu_feishu_sync.config import settings
    print("⚙️  配置信息:")
    print(f"   CookieCloud服务器: {settings.cookiecloud.server_url}")
    print(f"   飞书App ID: {settings.feishu.app_id}")
    print(f"   飞书Base ID: {settings.feishu.base_id}")
    print(f"   飞书Table ID: {settings.feishu.table_id}")
    print()
    
    # 执行各项测试
    results = {}
    
    results['cookiecloud'] = test_cookiecloud()
    results['yuntu_api'] = test_yuntu_api()
    results['feishu_api'] = test_feishu_api()
    results['data_flow'] = test_data_flow()
    
    # 汇总结果
    print("\n📊 测试结果汇总:")
    print("-" * 30)
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name:<15}: {status}")
    
    all_passed = all(results.values())
    print("-" * 30)
    
    if all_passed:
        print("🎉 所有测试通过！系统可以正常运行")
    else:
        print("⚠️  部分测试失败，请检查配置和网络连接")
        failed_tests = [name for name, success in results.items() if not success]
        print(f"失败的测试: {', '.join(failed_tests)}")

if __name__ == "__main__":
    main()
