#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script to fix logger calls in core modules
"""

import re
from pathlib import Path

def fix_logger_calls(file_path):
    """Fix logger calls in a file"""
    print(f"Fixing logger calls in {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Replace logger.xxx with self.logger.xxx
    content = re.sub(r'\blogger\.', 'self.logger.', content)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Fixed {file_path}")

def main():
    """Main function"""
    core_files = [
        'core/oa_client.py',
        'core/excel_processor.py',
        'core/feishu_client.py'
    ]
    
    for file_path in core_files:
        if Path(file_path).exists():
            fix_logger_calls(file_path)
        else:
            print(f"File not found: {file_path}")

if __name__ == "__main__":
    main()
