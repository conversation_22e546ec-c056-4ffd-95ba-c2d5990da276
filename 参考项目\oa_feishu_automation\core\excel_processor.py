#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel file processing for OA report data extraction
"""

import pandas as pd
from typing import Dict, List, Optional, Any
from pathlib import Path

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.field_mappings import FIELD_MAPPINGS
from utils.logger import setup_logger
from utils.validators import clean_text, validate_amount, validate_date
from utils.date_utils import parse_excel_date

class ExcelProcessor:
    """
    Processes Excel files downloaded from OA system
    """
    
    def __init__(self):
        """Initialize Excel processor"""
        self.logger = setup_logger()
        self.field_mappings = FIELD_MAPPINGS
    
    def read_excel_file(self, file_path: Path, sheet_name: Optional[str] = None) -> Optional[pd.DataFrame]:
        """
        Read Excel file into DataFrame
        
        Args:
            file_path: Path to Excel file
            sheet_name: Specific sheet name to read (None for first sheet)
            
        Returns:
            DataFrame if successful, None if failed
        """
        try:
            self.logger.info(f"Reading Excel file: {file_path}")
            
            # Try to read the Excel file
            if sheet_name:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
            else:
                df = pd.read_excel(file_path)
            
            self.logger.info(f"Successfully read Excel file with {len(df)} rows and {len(df.columns)} columns")
            return df
            
        except FileNotFoundError:
            self.logger.error(f"Excel file not found: {file_path}")
            return None
        except Exception as e:
            self.logger.error(f"Error reading Excel file: {e}")
            return None
    
    def analyze_excel_structure(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Analyze Excel file structure to understand data layout
        
        Args:
            df: DataFrame to analyze
            
        Returns:
            Dictionary with structure information
        """
        analysis = {
            'total_rows': len(df),
            'total_columns': len(df.columns),
            'column_names': list(df.columns),
            'data_types': df.dtypes.to_dict(),
            'null_counts': df.isnull().sum().to_dict(),
            'sample_data': {}
        }
        
        # Get sample data for first few rows
        for i, row in df.head(3).iterrows():
            analysis['sample_data'][f'row_{i}'] = row.to_dict()
        
        self.logger.info(f"Excel structure analysis completed: {analysis['total_rows']} rows, {analysis['total_columns']} columns")
        return analysis
    
    def detect_header_row(self, df: pd.DataFrame) -> int:
        """
        Detect which row contains the actual headers
        
        Args:
            df: DataFrame to analyze
            
        Returns:
            Row index of headers (0-based)
        """
        # Look for rows that contain typical header keywords
        header_keywords = ['编号', '申请', '日期', '金额', '状态', '审批', '部门', '姓名']
        
        for i, row in df.head(10).iterrows():
            # Count how many cells contain header-like text
            header_count = 0
            for cell in row:
                if isinstance(cell, str):
                    for keyword in header_keywords:
                        if keyword in cell:
                            header_count += 1
                            break
            
            # If more than half the cells look like headers, this is probably the header row
            if header_count > len(row) * 0.3:
                self.logger.info(f"Detected header row at index {i}")
                return i
        
        # Default to first row if no clear header found
        self.logger.info("No clear header row detected, using row 0")
        return 0
    
    def clean_dataframe(self, df: pd.DataFrame, header_row: int = 0) -> pd.DataFrame:
        """
        Clean and normalize DataFrame
        
        Args:
            df: Raw DataFrame
            header_row: Row index to use as headers
            
        Returns:
            Cleaned DataFrame
        """
        try:
            # Set proper headers
            if header_row > 0:
                df.columns = df.iloc[header_row]
                df = df.iloc[header_row + 1:].reset_index(drop=True)
            
            # Remove completely empty rows and columns
            df = df.dropna(how='all').dropna(axis=1, how='all')
            
            # Clean column names
            df.columns = [clean_text(col) for col in df.columns]
            
            # Clean text data
            for col in df.columns:
                if df[col].dtype == 'object':
                    df[col] = df[col].apply(lambda x: clean_text(x) if pd.notna(x) else "")
            
            self.logger.info(f"DataFrame cleaned: {len(df)} rows, {len(df.columns)} columns")
            return df
            
        except Exception as e:
            self.logger.error(f"Error cleaning DataFrame: {e}")
            return df
    
    def map_fields(self, df: pd.DataFrame, report_type: str) -> List[Dict[str, Any]]:
        """
        Map Excel columns to Feishu fields based on field mappings
        
        Args:
            df: Cleaned DataFrame
            report_type: Type of report ('contract', 'payment', 'prepayment')
            
        Returns:
            List of mapped records
        """
        if report_type not in self.field_mappings:
            self.logger.error(f"No field mapping found for report type: {report_type}")
            return []
        
        field_mapping = self.field_mappings[report_type]
        
        # Get field formatters if available
        from config.field_mappings import FIELD_FORMATTERS
        field_formatters = FIELD_FORMATTERS.get(report_type, {})
        
        mapped_records = []
        
        self.logger.info(f"Mapping {len(df)} records for {report_type} report")
        
        for index, row in df.iterrows():
            mapped_record = {}
            
            # Map each Excel column to Feishu field
            for excel_field, feishu_field in field_mapping.items():
                value = None
                
                # Use exact matching for Excel column names
                if excel_field in df.columns:
                    value = row[excel_field]
                else:
                    # Skip missing columns silently (some fields may not exist in all datasets)
                    continue
                
                # Apply field formatter if available
                if feishu_field in field_formatters:
                    try:
                        formatted_value = field_formatters[feishu_field](value)
                        if formatted_value:  # Only add non-empty values
                            mapped_record[feishu_field] = formatted_value
                    except Exception as e:
                        self.logger.warning(f"Failed to format field '{feishu_field}' value '{value}': {e}")
                        # Fallback to basic processing
                        processed_value = self._process_field_value(value, feishu_field)
                        if processed_value:
                            mapped_record[feishu_field] = processed_value
                else:
                    # Use basic processing
                    processed_value = self._process_field_value(value, feishu_field)
                    if processed_value:
                        mapped_record[feishu_field] = processed_value
            
            if mapped_record:  # Only add if we have some mapped data
                mapped_records.append(mapped_record)
        
        self.logger.info(f"Successfully mapped {len(mapped_records)} records")
        return mapped_records
    
    def _process_field_value(self, value: Any, field_name: str) -> Any:
        """
        Process field value based on field type
        
        Args:
            value: Raw value from Excel
            field_name: Feishu field name
            
        Returns:
            Processed value
        """
        if pd.isna(value) or value == "":
            return None
        
        # Date fields
        if any(keyword in field_name for keyword in ['日期', '时间']):
            return parse_excel_date(value)
        
        # Amount fields - convert to string for Feishu text fields
        elif any(keyword in field_name for keyword in ['金额', '费用', '价格']):
            try:
                # Remove currency symbols and format as string
                if isinstance(value, str):
                    cleaned_value = value.replace('¥', '').replace(',', '').replace('￥', '').strip()
                    if cleaned_value:
                        # Convert to float then back to string to normalize format
                        return str(float(cleaned_value))
                    return ""
                elif isinstance(value, (int, float)):
                    return str(value)
                return str(value) if value is not None else ""
            except (ValueError, TypeError):
                return ""
        
        # Text fields
        else:
            return clean_text(value)
    
    def process_excel_file(self, file_path: Path, report_type: str) -> List[Dict[str, Any]]:
        """
        Complete process: read, clean, and map Excel file
        
        Args:
            file_path: Path to Excel file
            report_type: Type of report
            
        Returns:
            List of processed records ready for Feishu upload
        """
        self.logger.info(f"Processing Excel file: {file_path} for {report_type} report")
        
        # Read Excel file
        df = self.read_excel_file(file_path)
        if df is None:
            return []
        
        # Analyze structure
        structure = self.analyze_excel_structure(df)
        self.logger.debug(f"Excel structure: {structure}")
        
        # Detect header row
        header_row = self.detect_header_row(df)
        
        # Clean DataFrame
        cleaned_df = self.clean_dataframe(df, header_row)
        
        # Map fields
        mapped_records = self.map_fields(cleaned_df, report_type)
        
        self.logger.info(f"Excel processing completed: {len(mapped_records)} records processed")
        return mapped_records
    
    def validate_records(self, records: List[Dict[str, Any]], report_type: str) -> List[Dict[str, Any]]:
        """
        Validate processed records
        
        Args:
            records: List of processed records
            report_type: Type of report
            
        Returns:
            List of valid records
        """
        valid_records = []
        
        for i, record in enumerate(records):
            is_valid = True
            
            # Basic validation - check for required fields
            required_fields = ['流程编号', '创建日期']  # Minimum required fields (use actual feishu field names)
            
            for field in required_fields:
                if field not in record or not record[field]:
                    self.logger.warning(f"Record {i} missing required field: {field}")
                    is_valid = False
                    break
            
            if is_valid:
                valid_records.append(record)
        
        self.logger.info(f"Validation completed: {len(valid_records)} valid records out of {len(records)}")
        return valid_records
