#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CookieCloud 单文件测试脚本
最快速验证CookieCloud服务可行性

使用方法:
1. 安装依赖: pip install PyCookieCloud
2. 修改下面的配置信息
3. 运行: python cookiecloud_final.py
"""

from PyCookieCloud import PyCookieCloud
import json
import sys

# ==================== 配置区域 ====================
# 请根据你的CookieCloud配置修改以下信息
SERVER_URL = "https://cnarbguabrg.zeabur.app"
UUID = "5csGiUGWFge13h6N3pvXYo"
PASSWORD = "31KEAXegEv5Khth6oLZ8fW"
# ================================================

def test_cookiecloud():
    """测试CookieCloud连接和数据获取"""
    print("🍪 CookieCloud 快速测试")
    print("=" * 40)
    print(f"🌐 服务器: {SERVER_URL}")
    print(f"🔑 UUID: {UUID}")
    print(f"🔐 密码: {PASSWORD[:8]}...")
    print()
    
    try:
        # 创建客户端并获取数据
        print("🚀 连接CookieCloud服务器...")
        client = PyCookieCloud(SERVER_URL, UUID, PASSWORD)
        
        print("📡 获取cookie数据...")
        cookies = client.get_decrypted_data()
        
        if not cookies:
            print("❌ 获取数据失败")
            return False
        
        # 统计信息
        total_cookies = 0
        domains = []
        
        for domain, cookie_list in cookies.items():
            if domain == 'update_time':
                continue
            if isinstance(cookie_list, list):
                total_cookies += len(cookie_list)
                domains.append((domain, len(cookie_list)))
        
        # 显示结果
        print("✅ 数据获取成功!")
        print(f"📊 统计信息:")
        print(f"   - 域名数量: {len(domains)}")
        print(f"   - Cookie总数: {total_cookies}")
        
        if 'update_time' in cookies:
            print(f"   - 更新时间: {cookies['update_time']}")
        
        # 显示前5个域名
        if domains:
            print(f"\n🌐 域名列表 (前5个):")
            domains.sort(key=lambda x: x[1], reverse=True)
            for i, (domain, count) in enumerate(domains[:5]):
                print(f"   {i+1}. {domain}: {count} 个cookie")
        
        # 保存数据
        output_file = "cookies_backup.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(cookies, f, ensure_ascii=False, indent=2)
        print(f"\n💾 数据已备份到: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    success = test_cookiecloud()
    
    if success:
        print("\n🎉 CookieCloud服务验证成功!")
        print("✨ 你的配置正确，可以正常获取cookie数据")
    else:
        print("\n💡 故障排查建议:")
        print("1. 检查服务器地址是否正确")
        print("2. 检查UUID和密码是否匹配")
        print("3. 确认网络连接正常")
        print("4. 检查CookieCloud服务是否正在运行")
        sys.exit(1)

if __name__ == "__main__":
    main() 