#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie management using CookieCloud for OA authentication
"""

import json
from typing import Dict, Optional, List
from PyCookieCloud import PyCookieCloud
from urllib.parse import urlparse

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import settings
from utils.logger import setup_logger

class CookieManager:
    """
    Manages cookies using CookieCloud for OA authentication
    """
    
    def __init__(self):
        """Initialize CookieManager with CookieCloud configuration"""
        self.logger = setup_logger()
        self.config = settings.cookiecloud
        self.client = None
        self.all_cookies = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize CookieCloud client"""
        try:
            self.client = PyCookieCloud(
                self.config.server_url,
                self.config.uuid,
                self.config.password
            )
            self.logger.info("CookieCloud client initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize CookieCloud client: {e}")
            raise
    
    def load_cookies(self) -> bool:
        """
        Load all cookies from CookieCloud
        
        Returns:
            True if cookies loaded successfully
        """
        try:
            self.logger.info("Loading cookies from CookieCloud...")
            self.all_cookies = self.client.get_decrypted_data()

            if not self.all_cookies:
                self.logger.error("Failed to get cookies from CookieCloud")
                return False

            self.logger.info(f"Successfully loaded cookies for {len(self.all_cookies)} domains")
            return True

        except Exception as e:
            self.logger.error(f"Error loading cookies: {e}")
            return False
    
    def get_domain_cookies(self, domain: str) -> List[Dict]:
        """
        Get cookies for a specific domain
        
        Args:
            domain: Target domain (e.g., 'oa.yaduo.com')
            
        Returns:
            List of cookie dictionaries for the domain
        """
        if not self.all_cookies:
            if not self.load_cookies():
                return []
        
        domain_cookies = []
        
        # Check exact domain match
        if domain in self.all_cookies:
            domain_cookies.extend(self.all_cookies[domain])
        
        # Check for cookies that apply to subdomains
        for cookie_domain, cookies in self.all_cookies.items():
            if cookie_domain == 'update_time':
                continue
                
            # Check if cookie domain matches or is a parent domain
            if (cookie_domain.startswith('.') and domain.endswith(cookie_domain[1:])) or \
               (domain == cookie_domain) or \
               (cookie_domain.startswith('.') and cookie_domain[1:] in domain):
                if isinstance(cookies, list):
                    domain_cookies.extend(cookies)
        
        self.logger.info(f"Found {len(domain_cookies)} cookies for domain {domain}")
        return domain_cookies
    
    def get_oa_cookies(self) -> Dict[str, str]:
        """
        Get OA system cookies in requests-compatible format
        
        Returns:
            Dictionary of cookie name-value pairs for OA domain
        """
        oa_domain = settings.oa.domain
        cookies = self.get_domain_cookies(oa_domain)
        
        cookie_dict = {}
        for cookie in cookies:
            name = cookie.get('name')
            value = cookie.get('value')
            if name and value:
                cookie_dict[name] = value
        
        self.logger.info(f"Prepared {len(cookie_dict)} cookies for OA authentication")
        return cookie_dict
    
    def get_cookie_header(self, domain: str) -> str:
        """
        Get cookies formatted as HTTP Cookie header
        
        Args:
            domain: Target domain
            
        Returns:
            Cookie header string
        """
        cookies = self.get_domain_cookies(domain)
        cookie_pairs = []
        
        for cookie in cookies:
            name = cookie.get('name')
            value = cookie.get('value')
            if name and value:
                cookie_pairs.append(f"{name}={value}")
        
        return '; '.join(cookie_pairs)
    
    def refresh_cookies(self) -> bool:
        """
        Refresh cookies from CookieCloud
        
        Returns:
            True if refresh successful
        """
        self.logger.info("Refreshing cookies from CookieCloud...")
        self.all_cookies = None
        return self.load_cookies()
    
    def validate_oa_cookies(self) -> bool:
        """
        Validate that required OA cookies are present
        
        Returns:
            True if required cookies are available
        """
        oa_cookies = self.get_oa_cookies()
        
        # Check for essential OA cookies
        required_cookies = ['ecology_JSessionid', 'loginidweaver']
        missing_cookies = [cookie for cookie in required_cookies if cookie not in oa_cookies]
        
        if missing_cookies:
            self.logger.warning(f"Missing required OA cookies: {missing_cookies}")
            return False

        self.logger.info("OA cookies validation passed")
        return True
    
    def get_cookies_info(self) -> Dict:
        """
        Get information about available cookies
        
        Returns:
            Dictionary with cookie statistics
        """
        if not self.all_cookies:
            self.load_cookies()
        
        if not self.all_cookies:
            return {"total_domains": 0, "total_cookies": 0}
        
        total_cookies = 0
        domains = []
        
        for domain, cookies in self.all_cookies.items():
            if domain == 'update_time':
                continue
            if isinstance(cookies, list):
                total_cookies += len(cookies)
                domains.append(domain)
        
        return {
            "total_domains": len(domains),
            "total_cookies": total_cookies,
            "domains": domains,
            "update_time": self.all_cookies.get('update_time', 'Unknown')
        }
