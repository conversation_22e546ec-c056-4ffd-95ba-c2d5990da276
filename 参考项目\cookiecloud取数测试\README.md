# CookieCloud 取数测试

这是一个用于验证CookieCloud服务可行性的Python测试脚本集合，包含通用的cookie获取工具。

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

或者单独安装：

```bash
pip install PyCookieCloud python-dotenv
```

### 2. 配置环境变量

复制并编辑 `.env` 文件：

```bash
# CookieCloud 配置信息
COOKIECLOUD_SERVER_URL=https://your-server.com
COOKIECLOUD_UUID=your-uuid
COOKIECLOUD_PASSWORD=your-password

# 默认输出格式 (json, netscape, requests)
DEFAULT_OUTPUT_FORMAT=json
```

### 3. 使用工具

#### 🌟 Cookie获取工具 (推荐)

```bash
# 获取指定网站的cookies
python cookie_util.py github.com

# 显示cookie列表
python cookie_util.py github.com --list

# 生成Python requests代码
python cookie_util.py github.com --format requests --output github_cookies.py

# 生成Netscape格式 (适用于curl)
python cookie_util.py github.com --format netscape --output github.txt
```

#### 基础测试脚本

```bash
# 验证CookieCloud连接
python cookiecloud_final.py
```

## 📁 文件说明

### 🔧 工具文件
- **`cookie_util.py`** - 🌟 **主要工具** - 获取指定网站cookie的通用工具
- **`cookiecloud_final.py`** - 快速验证CookieCloud连接的测试脚本
- **`.env`** - 配置文件（需要根据你的环境修改）

### 📚 其他文件
- `cookiecloud_simple.py` - 使用官方库的详细测试脚本
- `cookiecloud_test.py` - 自实现解密逻辑的测试脚本（仅供学习）
- `requirements.txt` - 依赖包列表

## 🛠️ Cookie工具详细用法

### 基本用法

```bash
# 获取指定域名的cookies (JSON格式)
python cookie_util.py example.com

# 显示详细信息
python cookie_util.py example.com --verbose

# 只显示cookie名称列表
python cookie_util.py example.com --list
```

### 输出格式

#### 1. JSON格式 (默认)
```bash
python cookie_util.py github.com --format json
```

#### 2. Netscape格式 (适用于curl等工具)
```bash
python cookie_util.py github.com --format netscape
# 使用方法: curl -b cookies.txt https://github.com
```

#### 3. Python requests格式
```bash
python cookie_util.py github.com --format requests --output github_cookies.py
# 生成可直接使用的Python代码
```

### 保存到文件

```bash
# 保存JSON格式
python cookie_util.py github.com --output github.json

# 保存Netscape格式
python cookie_util.py github.com --format netscape --output github.txt

# 保存Python代码
python cookie_util.py github.com --format requests --output github_cookies.py
```

## ⚙️ 配置说明

在 `.env` 文件中配置你的CookieCloud信息：

```env
# CookieCloud服务器配置
COOKIECLOUD_SERVER_URL=https://your-cookiecloud-server.com
COOKIECLOUD_UUID=your-user-uuid
COOKIECLOUD_PASSWORD=your-encryption-password

# 默认输出格式
DEFAULT_OUTPUT_FORMAT=json
```

## 📊 使用示例

### 示例1：获取GitHub cookies用于爬虫

```bash
# 生成Python代码
python cookie_util.py github.com --format requests --output github_cookies.py

# 使用生成的代码
python github_cookies.py
```

### 示例2：获取cookies用于curl

```bash
# 生成Netscape格式
python cookie_util.py github.com --format netscape --output github.txt

# 使用curl
curl -b github.txt https://github.com/settings
```

### 示例3：查看网站的所有cookies

```bash
# 显示cookie列表
python cookie_util.py github.com --list

# 查看详细JSON数据
python cookie_util.py github.com --format json
```

## 🔧 故障排查

如果工具运行失败，请检查：

1. **配置文件** - 确认 `.env` 文件中的配置正确
2. **网络连接** - 确认能访问CookieCloud服务器
3. **域名拼写** - 确认目标域名拼写正确
4. **数据同步** - 确认浏览器已登录目标网站且CookieCloud已同步

## 📝 输出示例

### Cookie列表输出
```
🍪 github.com 的Cookie列表:
   1. _octo (域名: .github.com)
   2. user_session (域名: github.com)
   3. logged_in (域名: .github.com)
   ...
```

### Python requests代码输出
```python
import requests

COOKIES = {
    "_octo": "GH1.1.1234567890.1735876896",
    "user_session": "your-session-token",
    "logged_in": "yes"
}

def make_request_with_cookies(url):
    response = requests.get(url, cookies=COOKIES)
    return response
```

## 🔗 相关链接

- [CookieCloud 官方项目](https://github.com/easychen/CookieCloud)
- [PyCookieCloud 库](https://github.com/lupohan44/PyCookieCloud) 