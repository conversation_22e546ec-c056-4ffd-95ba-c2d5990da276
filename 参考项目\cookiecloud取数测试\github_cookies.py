#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
github.com 网站的Cookie配置
由CookieCloud Util自动生成于 2025-06-04T12:32:57.597871
"""

import requests

# Cookie字典
COOKIES = {
    "_octo": "GH1.1.1234544226.1735876896",
    "cpu_bucket": "xlg",
    "preferred_color_mode": "light",
    "tz": "Asia%2FShanghai",
    "color_mode": "%7B%22color_mode%22%3A%22auto%22%2C%22light_theme%22%3A%7B%22name%22%3A%22light%22%2C%22color_mode%22%3A%22light%22%7D%2C%22dark_theme%22%3A%7B%22name%22%3A%22dark%22%2C%22color_mode%22%3A%22dark%22%7D%7D",
    "GHCC": "Required:1-Analytics:1-SocialMedia:1-Advertising:1",
    "logged_in": "yes",
    "dotcom_user": "Xuleileon",
    "_device_id": "80eba56ad4bc7ac05a19166d0d17592a",
    "trackingAllowed": "false",
    "loginbox_strategy": "1",
    "popShowed10s": "yes",
    "unlogin_scroll_step": "1",
    "anthropic-consent-preferences": "%7B%22analytics%22%3Afalse%2C%22marketing%22%3Afalse%7D",
    "consent": "rejected",
    "saved_user_sessions": "149945786%3A8RT5c2Skn2Mu2p-tCvcqTdAfsohjh4nG9M1hwGR1goX3MbT8",
    "user_session": "8RT5c2Skn2Mu2p-tCvcqTdAfsohjh4nG9M1hwGR1goX3MbT8",
    "__Host-user_session_same_site": "8RT5c2Skn2Mu2p-tCvcqTdAfsohjh4nG9M1hwGR1goX3MbT8",
    "_gh_sess": "Bwa9vrzwpbvKSto1Oyp%2BL%2F6%2BXtdSGKutz%2BQHL7rVOXpwTPOD11rprbxm%2FePqq09XIVWGwzpI3usryHQNbpO7gyUn%2F4V5dYgKOMgNtHjXRJifycroVE%2F0qE2VyJTMUrbSQEj4ufoPvOe5uwTSIrzVXLc1HLopoXBmtSfnpfQe4S4DS0Er69zE2flQjq7MMuABU2CR0W%2B03k%2FiDc4D%2FEnGVJQSJMK7MqTsbqc%2FFkbdHv1h42MF7tDU69QwbW9IfAFNtbamBJoR1n4QRd1PZnhmlZNUNrbI%2BSmgYZcCDdJ9yTAZOTZAPWCJBI2mnIdb2zuNoKGlznXo459okAVubgzeu8R3knfM3DawfmPI2XXMYyeqpn5Q5IRuZRT5ywMBrWrxpEPjvu6SzBTKw54X8uTplODDX%2BOysbQA%2F2whJfDtwg4RD2pdxd%2BKVsrbC%2Bpzm4jxHyjaUYpMH7l2TkGQ%2FIBOjmeFE4sDJBxUor0qOHMbuz8jEB7Aco41gHNC3s27pydtIZMqF0tem022%2BA7Ka6TpHiIYrdxKtDUEcEJiU1nzqeoKBit%2BvaK0ovAzNLE%3D--JloRi948Nv1k4RqP--qDP3gMrX2VO9oxE9UujD6Q%3D%3D",
    "_docs-events": "f99d8248-f6c6-401c-8b2e-918a6dbf8706",
    "toolPreferred": "windowsterminal"
}

# 使用示例
def make_request_with_cookies(url):
    """使用cookies发送请求"""
    response = requests.get(url, cookies=COOKIES)
    return response

# 直接使用示例
if __name__ == "__main__":
    # 示例：访问github.com
    url = "https://github.com"
    response = make_request_with_cookies(url)
    print(f"状态码: {response.status_code}")
    print(f"响应长度: {len(response.text)}")
