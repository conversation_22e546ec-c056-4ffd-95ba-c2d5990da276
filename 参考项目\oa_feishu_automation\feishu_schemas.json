{"contract_table": {"table_info": null, "fields": [{"field_id": "fld3yPkA2p", "field_name": "流程编号", "field_type": 1, "description": null}, {"field_id": "fld2EDdyKn", "field_name": "创建人", "field_type": 1, "description": null}, {"field_id": "fld2JvNZdC", "field_name": "我方公司", "field_type": 3, "description": null}, {"field_id": "fld4TdyEfH", "field_name": "对方公司", "field_type": 1, "description": null}, {"field_id": "fldh5mhfVG", "field_name": "合同金额（标的金额）", "field_type": 1, "description": null}, {"field_id": "fldxfZgmzu", "field_name": "收|付款阶段", "field_type": 3, "description": null}, {"field_id": "fld51YCAad", "field_name": "收|付款条件", "field_type": 1, "description": null}, {"field_id": "fld1Iqxiah", "field_name": "收|付款金额", "field_type": 1, "description": null}, {"field_id": "fld71JZZAo", "field_name": "收|付款比例", "field_type": 1, "description": null}, {"field_id": "fld4xTbPqZ", "field_name": "招标比价编号", "field_type": 1, "description": null}, {"field_id": "fld3uZYJoU", "field_name": "主要内容", "field_type": 1, "description": null}, {"field_id": "fldxniAopb", "field_name": "流程审批类型", "field_type": 3, "description": null}, {"field_id": "fld72f2P1E", "field_name": "创建日期", "field_type": 1, "description": null}, {"field_id": "fld7qFzoOz", "field_name": "合同类型", "field_type": 3, "description": null}, {"field_id": "fld5gdKSg6", "field_name": "是否关联原合同", "field_type": 3, "description": null}, {"field_id": "fld4ttw6CI", "field_name": "原合同", "field_type": 1, "description": null}, {"field_id": "fld6c3kHxW", "field_name": "原合同名称", "field_type": 1, "description": null}, {"field_id": "fld2ssAipI", "field_name": "是否需要招标比价", "field_type": 3, "description": null}, {"field_id": "fld6I39Tqo", "field_name": "当前节点", "field_type": 1, "description": null}, {"field_id": "fld5kL6rM8", "field_name": "签字意见", "field_type": 1, "description": null}, {"field_id": "fld4Qpl4Dm", "field_name": "合同审核稿上传", "field_type": 1, "description": null}, {"field_id": "fld3ilnpeV", "field_name": "资质上传", "field_type": 1, "description": null}, {"field_id": "fld3MYhmfN", "field_name": "双方盖章后合同上传", "field_type": 1, "description": null}, {"field_id": "fld2NitnKv", "field_name": "固定期间", "field_type": 3, "description": null}, {"field_id": "fld9Rb6XMu", "field_name": "合同期限", "field_type": 1, "description": null}, {"field_id": "fld7Ip0Svc", "field_name": "开始日期", "field_type": 1, "description": null}, {"field_id": "fld2b4ukVC", "field_name": "截至日期", "field_type": 1, "description": null}, {"field_id": "fldpoa3gFI", "field_name": "是否框架合同", "field_type": 3, "description": null}, {"field_id": "fldQy1j2nA", "field_name": "送审部门", "field_type": 1, "description": null}, {"field_id": "fld5X1KRyX", "field_name": "合同名称", "field_type": 1, "description": null}]}, "payment_table": {"table_info": null, "fields": [{"field_id": "fld3DDpgSH", "field_name": "流程编号", "field_type": 1, "description": null}, {"field_id": "fldRXN1v9d", "field_name": "合同规定付款条件", "field_type": 1, "description": null}, {"field_id": "fld5zTdLht", "field_name": "税额", "field_type": 1, "description": null}, {"field_id": "fld2y7XhCd", "field_name": "去税金额", "field_type": 1, "description": null}, {"field_id": "fld7BqKfvf", "field_name": "附件", "field_type": 1, "description": null}, {"field_id": "fld60ianDx", "field_name": "创建日期", "field_type": 1, "description": null}, {"field_id": "fld2XbeDyp", "field_name": "当前节点", "field_type": 3, "description": null}, {"field_id": "fldSe3jLd4", "field_name": "签字意见", "field_type": 1, "description": null}, {"field_id": "fld3GM1DqJ", "field_name": "费用类型", "field_type": 1, "description": null}, {"field_id": "fld4zzXyTP", "field_name": "供应商名称", "field_type": 1, "description": null}, {"field_id": "fld4slbVwV", "field_name": "是否有合同流程", "field_type": 3, "description": null}, {"field_id": "fld3c7TM5x", "field_name": "企业名称", "field_type": 3, "description": null}, {"field_id": "fld2m0h7tO", "field_name": "款项用途", "field_type": 1, "description": null}, {"field_id": "fld3axiutR", "field_name": "本次支付金额", "field_type": 1, "description": null}, {"field_id": "fld5DSb6lZ", "field_name": "专票税额（不是专票填写0）", "field_type": 1, "description": null}, {"field_id": "fld1YvM7EP", "field_name": "去税总金额", "field_type": 1, "description": null}, {"field_id": "fld6yO3YJq", "field_name": "创建人", "field_type": 1, "description": null}, {"field_id": "fld18UW1Sw", "field_name": "费用所属部门", "field_type": 1, "description": null}, {"field_id": "fld1xLz5nR", "field_name": "相关合同", "field_type": 1, "description": null}, {"field_id": "fld7ywxrZE", "field_name": "创建人所属部门", "field_type": 1, "description": null}, {"field_id": "fld3yLOWVz", "field_name": "金额", "field_type": 1, "description": null}, {"field_id": "fld6FWj5EV", "field_name": "选择发票", "field_type": 1, "description": null}, {"field_id": "fld2N4T8xf", "field_name": "发票类型", "field_type": 3, "description": null}, {"field_id": "fld1UjT7gz", "field_name": "税率", "field_type": 1, "description": null}]}}