#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理器 - 处理云图数据并转换为飞书格式
"""

from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

from ..config import FIELD_MAPPING
from ..utils import setup_logger

class DataProcessor:
    """
    数据处理器 - 将云图数据转换为飞书多维表格式
    """
    
    def __init__(self):
        """初始化数据处理器"""
        self.logger = setup_logger()
    
    def process_yuntu_data(self, yuntu_data: Dict[str, Any],
                          data_period: str) -> List[Dict[str, Any]]:
        """
        处理云图数据，转换为飞书记录格式

        Args:
            yuntu_data: 云图API返回的数据
            data_period: 数据周期（如2025/05）

        Returns:
            飞书记录列表
        """
        if not yuntu_data:
            self.logger.warning("云图数据为空")
            return []

        distribution_list = yuntu_data.get('distribution_list', [])
        if not distribution_list:
            self.logger.warning("distribution_list为空")
            return []

        records = []

        for item in distribution_list:
            try:
                record = self._convert_item_to_record(item, data_period)
                if record:
                    records.append(record)
            except Exception as e:
                self.logger.error(f"处理数据项时出错: {e}")
                continue

        self.logger.info(f"成功处理 {len(records)} 条记录")
        return records

    def process_historical_data(self, historical_data: List[Tuple[str, Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """
        处理历史数据

        Args:
            historical_data: (数据周期, 数据)的列表

        Returns:
            所有记录的列表
        """
        all_records = []

        for data_period, yuntu_data in historical_data:
            self.logger.info(f"处理 {data_period} 的数据...")
            records = self.process_yuntu_data(yuntu_data, data_period)
            all_records.extend(records)

        self.logger.info(f"历史数据处理完成，共 {len(all_records)} 条记录")
        return all_records
    
    def _convert_item_to_record(self, item: Dict[str, Any], data_period: str) -> Optional[Dict[str, Any]]:
        """
        将单个云图数据项转换为飞书记录

        Args:
            item: 云图数据项
            data_period: 数据周期（如2025/05）

        Returns:
            飞书记录字典
        """
        try:
            # 提取触点信息
            trigger_point = item.get('trigger_point', {})
            basic_index = item.get('basic_index', {})
            bm_basic_index = item.get('bm_basic_index', {})
            
            # 构建记录
            record = {}
            
            # 触点信息
            if 'trigger_point_id' in trigger_point:
                record[FIELD_MAPPING['trigger_point_id']] = trigger_point['trigger_point_id']
            
            if 'trigger_point_name' in trigger_point:
                record[FIELD_MAPPING['trigger_point_name']] = trigger_point['trigger_point_name']
            
            # 基础指标
            if basic_index:
                if 'convert_uv' in basic_index:
                    record[FIELD_MAPPING['convert_uv']] = self._safe_convert_number(basic_index['convert_uv'])
                
                if 'convert_rate' in basic_index:
                    record[FIELD_MAPPING['convert_rate']] = self._safe_convert_number(basic_index['convert_rate'])
                
                if 'convert_new_rate' in basic_index:
                    record[FIELD_MAPPING['convert_new_rate']] = self._safe_convert_number(basic_index['convert_new_rate'])
                
                if 'average_transaction_value' in basic_index:
                    record[FIELD_MAPPING['average_transaction_value']] = self._safe_convert_number(basic_index['average_transaction_value'])
            
            # 基准指标
            if bm_basic_index:
                if 'convert_uv' in bm_basic_index:
                    record[FIELD_MAPPING['bm_convert_uv']] = self._safe_convert_number(bm_basic_index['convert_uv'])
                
                if 'convert_rate' in bm_basic_index:
                    record[FIELD_MAPPING['bm_convert_rate']] = self._safe_convert_number(bm_basic_index['convert_rate'])
                
                if 'convert_new_rate' in bm_basic_index:
                    record[FIELD_MAPPING['bm_convert_new_rate']] = self._safe_convert_number(bm_basic_index['convert_new_rate'])
                
                if 'average_transaction_value' in bm_basic_index:
                    record[FIELD_MAPPING['bm_average_transaction_value']] = self._safe_convert_number(bm_basic_index['average_transaction_value'])
            
            # 元数据
            record[FIELD_MAPPING['data_period']] = data_period
            
            return record
            
        except Exception as e:
            self.logger.error(f"转换数据项时出错: {e}")
            return None
    
    def _safe_convert_number(self, value: Any) -> Optional[float]:
        """
        安全地转换数值
        
        Args:
            value: 要转换的值
            
        Returns:
            转换后的数值或None
        """
        if value is None:
            return None
        
        if isinstance(value, (int, float)):
            return float(value)
        
        if isinstance(value, str):
            try:
                # 尝试转换字符串数值
                if value.strip() == '':
                    return None
                return float(value)
            except ValueError:
                self.logger.warning(f"无法转换数值: {value}")
                return None
        
        self.logger.warning(f"未知数值类型: {type(value)}, 值: {value}")
        return None
    
    def validate_records(self, records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        验证记录数据
        
        Args:
            records: 记录列表
            
        Returns:
            验证后的记录列表
        """
        valid_records = []
        
        for i, record in enumerate(records):
            try:
                # 检查必需字段
                required_fields = [FIELD_MAPPING['trigger_point_name'], FIELD_MAPPING['data_period']]

                missing_fields = []
                for field in required_fields:
                    if field not in record or not record[field]:
                        missing_fields.append(field)

                if missing_fields:
                    self.logger.warning(f"记录 {i} 缺少必需字段: {missing_fields}")
                    continue
                
                # 验证数值字段
                number_fields = [
                    FIELD_MAPPING['convert_uv'],
                    FIELD_MAPPING['convert_rate'],
                    FIELD_MAPPING['convert_new_rate'],
                    FIELD_MAPPING['average_transaction_value'],
                    FIELD_MAPPING['bm_convert_uv'],
                    FIELD_MAPPING['bm_convert_rate'],
                    FIELD_MAPPING['bm_convert_new_rate'],
                    FIELD_MAPPING['bm_average_transaction_value']
                ]
                
                for field in number_fields:
                    if field in record and record[field] is not None:
                        if not isinstance(record[field], (int, float)):
                            self.logger.warning(f"记录 {i} 字段 {field} 不是数值类型")
                            record[field] = None
                
                valid_records.append(record)
                
            except Exception as e:
                self.logger.error(f"验证记录 {i} 时出错: {e}")
                continue
        
        self.logger.info(f"验证完成: {len(valid_records)}/{len(records)} 条记录有效")
        return valid_records
    
    def deduplicate_records(self, records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        去重记录（基于触点名称和数据周期）

        Args:
            records: 记录列表

        Returns:
            去重后的记录列表
        """
        seen = set()
        unique_records = []

        for record in records:
            # 创建唯一键：触点名称 + 数据周期
            trigger_name = record.get(FIELD_MAPPING['trigger_point_name'], '')
            data_period = record.get(FIELD_MAPPING['data_period'], '')
            key = f"{trigger_name}_{data_period}"

            if key not in seen:
                seen.add(key)
                unique_records.append(record)
            else:
                self.logger.debug(f"发现重复记录: {key}")

        if len(unique_records) != len(records):
            self.logger.info(f"去重完成: {len(records)} -> {len(unique_records)} 条记录")

        return unique_records

    def create_unique_key(self, record: Dict[str, Any]) -> str:
        """
        为记录创建唯一键，用于增量更新判断

        Args:
            record: 记录

        Returns:
            唯一键字符串
        """
        trigger_name = record.get(FIELD_MAPPING['trigger_point_name'], '')
        data_period = record.get(FIELD_MAPPING['data_period'], '')
        return f"{trigger_name}_{data_period}"
