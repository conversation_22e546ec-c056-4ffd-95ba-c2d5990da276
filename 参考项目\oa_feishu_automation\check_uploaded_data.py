#!/usr/bin/env python3
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.feishu_client import FeishuClient
from config.settings import settings
from utils.logger import setup_logger

def check_uploaded_data():
    """检查上传到飞书的数据"""
    logger = setup_logger()
    
    try:
        # 初始化飞书客户端
        feishu_client = FeishuClient()
        
        # 获取payment表的数据
        table_id = settings.feishu.payment_table_id
        logger.info(f"获取表格 {table_id} 的数据...")
        
        # 获取表格schema
        fields = feishu_client.get_table_fields(table_id)
        if fields:
            print("=== 飞书表格字段信息 ===")
            for i, field in enumerate(fields, 1):
                print(f"{i:2d}. {field.field_name} (类型: {field.field_type})")
        
        # 获取最新的几条记录
        records = feishu_client.get_table_records(table_id, page_size=5)
        if records:
            print(f"\n=== 最新 {len(records)} 条记录 ===")
            for i, record in enumerate(records, 1):
                print(f"\n--- 记录 {i} ---")
                for field_name, field_value in record.items():
                    if field_value and str(field_value).strip():  # 只显示有值的字段
                        print(f"  {field_name}: {field_value}")
        else:
            print("未找到记录")
            
    except Exception as e:
        logger.error(f"检查数据时出错: {e}")

if __name__ == "__main__":
    check_uploaded_data() 