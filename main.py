#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云图数据同步到飞书多维表 - 主程序
"""

import argparse
import sys
from datetime import datetime, timedelta
from pathlib import Path

from yuntu_feishu_sync import SyncManager
from yuntu_feishu_sync.utils import setup_logger

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="云图数据同步到飞书多维表工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py --latest                 # 同步最新数据（推荐）
  python main.py --historical             # 同步所有历史数据
  python main.py --historical --start-year 2024 --start-month 6  # 从2024年6月开始同步
  python main.py --test                   # 测试所有连接
  python main.py --schema                 # 读取表格结构
        """
    )
    
    parser.add_argument(
        '--historical',
        action='store_true',
        help='同步历史数据（从2024年开始）'
    )

    parser.add_argument(
        '--latest',
        action='store_true',
        help='同步最新数据（当前月或上个月）'
    )

    parser.add_argument(
        '--start-year',
        type=int,
        default=2024,
        help='历史数据开始年份（默认2024）'
    )

    parser.add_argument(
        '--start-month',
        type=int,
        default=1,
        help='历史数据开始月份（默认1）'
    )

    parser.add_argument(
        '--max-months',
        type=int,
        default=24,
        help='最多同步多少个月的历史数据（默认24）'
    )
    
    parser.add_argument(
        '--test', 
        action='store_true', 
        help='测试所有连接'
    )
    
    parser.add_argument(
        '--schema', 
        action='store_true', 
        help='读取并保存表格结构'
    )
    
    parser.add_argument(
        '--verbose', '-v', 
        action='store_true', 
        help='详细输出'
    )
    
    return parser.parse_args()

def validate_date(date_str: str) -> bool:
    """验证日期格式"""
    try:
        datetime.strptime(date_str, '%Y-%m-%d')
        return True
    except ValueError:
        return False

def main():
    """主函数"""
    args = parse_arguments()
    
    # 设置日志级别
    log_level = "DEBUG" if args.verbose else "INFO"
    logger = setup_logger(level=log_level)
    
    print("🚀 云图数据同步到飞书多维表工具")
    print("=" * 50)
    
    try:
        # 初始化同步管理器
        sync_manager = SyncManager()
        
        # 测试连接
        if args.test:
            print("🧪 执行连接测试...")
            results = sync_manager.test_all_connections()
            
            print("\n📊 测试结果:")
            for service, success in results.items():
                status = "✅ 成功" if success else "❌ 失败"
                print(f"  {service}: {status}")
            
            if all(results.values()):
                print("\n🎉 所有连接测试通过！")
                return 0
            else:
                print("\n⚠️  部分连接测试失败，请检查配置")
                return 1
        
        # 读取表格结构
        if args.schema:
            print("📋 读取表格结构...")
            schema = sync_manager.get_table_schema()
            
            if schema:
                fields = schema.get('fields', [])
                print(f"✅ 成功读取表格结构: {len(fields)} 个字段")
                
                # 保存到文件
                file_path = sync_manager.save_table_schema()
                print(f"💾 结构已保存到: {file_path}")
                
                # 显示字段信息
                print("\n📝 字段列表:")
                for i, field in enumerate(fields, 1):
                    print(f"  {i:2d}. {field['field_name']} (类型: {field['field_type']})")
                
                return 0
            else:
                print("❌ 读取表格结构失败")
                return 1
        
        # 数据同步
        if args.historical:
            # 历史数据同步
            print(f"📅 历史数据同步: {args.start_year}年{args.start_month}月开始，最多{args.max_months}个月")
            print()

            print("🔄 开始历史数据同步...")
            result = sync_manager.sync_historical_data(
                start_year=args.start_year,
                start_month=args.start_month,
                max_months=args.max_months
            )

        elif args.latest:
            # 最新数据同步
            print("📅 最新数据同步: 当前月或上个月")
            print()

            print("🔄 开始最新数据同步...")
            result = sync_manager.sync_latest_data()

        else:
            # 默认同步最新数据
            print("📅 默认同步最新数据（使用 --latest 或 --historical 指定模式）")
            print()

            print("🔄 开始最新数据同步...")
            result = sync_manager.sync_latest_data()
        
        if result.get('success'):
            print("✅ 数据同步成功！")
            print(f"📊 同步统计:")

            if 'months_processed' in result:
                print(f"   处理月份数: {result.get('months_processed', 0)}")
            if 'data_period' in result:
                print(f"   数据周期: {result.get('data_period', '')}")

            print(f"   总记录数: {result.get('total_records', 0)}")
            print(f"   新增记录: {result.get('added', 0)}")
            print(f"   更新记录: {result.get('updated', 0)}")
            print(f"   跳过记录: {result.get('skipped', 0)}")
            print(f"   失败记录: {result.get('failed', 0)}")

            if result.get('failed', 0) > 0:
                print("⚠️  部分记录同步失败，请检查日志")
                return 1

            return 0
        else:
            print(f"❌ 数据同步失败: {result.get('error', '未知错误')}")
            return 1
    
    except KeyboardInterrupt:
        print("\n⏹️  用户中断操作")
        return 1
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        print(f"❌ 程序执行出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
