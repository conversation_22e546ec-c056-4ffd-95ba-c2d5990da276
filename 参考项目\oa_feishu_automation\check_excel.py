#!/usr/bin/env python3
import pandas as pd
import sys

if len(sys.argv) > 1:
    file_path = sys.argv[1]
else:
    file_path = 'downloads/预付款申请单-20250604152754.xlsx'

print(f"分析文件: {file_path}")

try:
    # 读取Excel文件
    df = pd.read_excel(file_path)
    print(f"形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    print("\n前5行数据:")
    print(df.head())
    
    print("\n查找包含关键字段的行:")
    for i, row in df.iterrows():
        row_str = ' '.join([str(x) for x in row if pd.notna(x)])
        if any(keyword in row_str for keyword in ['申请编号', '流程编号', '申请人', '申请日期']):
            print(f"第{i}行: {list(row)}")
            
except Exception as e:
    print(f"错误: {e}") 