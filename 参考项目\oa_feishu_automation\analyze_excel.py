#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Analyze Excel reference files to understand structure and update field mappings
"""

import pandas as pd
import json
from pathlib import Path
from typing import Dict, Any

from utils.logger import setup_logger

def analyze_excel_file(file_path: Path) -> Dict[str, Any]:
    """
    Analyze a single Excel file
    
    Args:
        file_path: Path to Excel file
        
    Returns:
        Analysis results
    """
    logger = setup_logger()
    logger.info(f"Analyzing Excel file: {file_path}")
    
    try:
        # Read Excel file
        df = pd.read_excel(file_path)
        
        analysis = {
            "file_name": file_path.name,
            "total_rows": len(df),
            "total_columns": len(df.columns),
            "column_names": list(df.columns),
            "data_types": df.dtypes.astype(str).to_dict(),
            "null_counts": df.isnull().sum().to_dict(),
            "sample_data": {},
            "potential_headers": []
        }
        
        # Get sample data for first few rows
        for i, row in df.head(5).iterrows():
            analysis["sample_data"][f"row_{i}"] = {
                col: str(val) if pd.notna(val) else None 
                for col, val in row.items()
            }
        
        # Look for potential header rows
        for i, row in df.head(10).iterrows():
            header_score = 0
            row_data = []
            
            for cell in row:
                if isinstance(cell, str) and len(cell.strip()) > 0:
                    cell_clean = cell.strip()
                    row_data.append(cell_clean)
                    
                    # Score based on typical header characteristics
                    if any(keyword in cell_clean for keyword in 
                          ['编号', '申请', '日期', '金额', '状态', '审批', '部门', '姓名', '名称', '类型']):
                        header_score += 2
                    elif len(cell_clean) < 20 and not cell_clean.isdigit():
                        header_score += 1
                else:
                    row_data.append(None)
            
            if header_score > 2:
                analysis["potential_headers"].append({
                    "row_index": i,
                    "score": header_score,
                    "data": row_data
                })
        
        logger.info(f"Analysis completed for {file_path.name}")
        return analysis
        
    except Exception as e:
        logger.error(f"Error analyzing {file_path}: {e}")
        return {"error": str(e), "file_name": file_path.name}

def suggest_field_mappings(analysis: Dict[str, Any], report_type: str) -> Dict[str, str]:
    """
    Suggest field mappings based on Excel analysis
    
    Args:
        analysis: Excel analysis results
        report_type: Type of report
        
    Returns:
        Suggested field mappings
    """
    mappings = {}
    
    # Get the best header row
    if analysis.get("potential_headers"):
        best_header = max(analysis["potential_headers"], key=lambda x: x["score"])
        headers = [h for h in best_header["data"] if h]
        
        logger = setup_logger()
        logger.info(f"Best header row (index {best_header['row_index']}): {headers}")
        
        # Common field mapping patterns
        field_patterns = {
            # Contract fields
            "contract": {
                "流程编号": ["流程编号", "编号", "ID"],
                "申请编号": ["申请编号", "申请号"],
                "申请日期": ["申请日期", "创建日期", "日期"],
                "申请人": ["申请人", "创建人", "姓名"],
                "申请部门": ["申请部门", "部门"],
                "合同名称": ["合同名称", "标题", "名称"],
                "合同类型": ["合同类型", "类型"],
                "合同金额": ["合同金额", "金额", "总金额"],
                "合同相对方": ["合同相对方", "相对方", "对方"],
                "审批状态": ["审批状态", "状态", "流程状态"],
                "审批日期": ["审批日期", "完成日期"],
                "备注": ["备注", "说明", "描述"]
            },
            # Payment fields
            "payment": {
                "流程编号": ["流程编号", "编号", "ID"],
                "申请编号": ["申请编号", "申请号"],
                "申请日期": ["申请日期", "创建日期", "日期"],
                "申请人": ["申请人", "创建人", "姓名"],
                "申请部门": ["申请部门", "部门"],
                "付款类型": ["付款类型", "类型"],
                "付款金额": ["付款金额", "金额", "总金额"],
                "收款方": ["收款方", "收款人", "对方"],
                "付款用途": ["付款用途", "用途", "说明"],
                "审批状态": ["审批状态", "状态", "流程状态"],
                "审批日期": ["审批日期", "完成日期"],
                "付款日期": ["付款日期", "实际付款日期"],
                "备注": ["备注", "说明", "描述"]
            },
            # Prepayment fields
            "prepayment": {
                "流程编号": ["流程编号", "编号", "ID"],
                "申请编号": ["申请编号", "申请号"],
                "申请日期": ["申请日期", "创建日期", "日期"],
                "申请人": ["申请人", "创建人", "姓名"],
                "申请部门": ["申请部门", "部门"],
                "预付款类型": ["预付款类型", "类型"],
                "预付款金额": ["预付款金额", "金额", "总金额"],
                "收款方": ["收款方", "收款人", "对方"],
                "预付款用途": ["预付款用途", "用途", "说明"],
                "审批状态": ["审批状态", "状态", "流程状态"],
                "审批日期": ["审批日期", "完成日期"],
                "预计付款日期": ["预计付款日期", "计划付款日期"],
                "备注": ["备注", "说明", "描述"]
            }
        }
        
        patterns = field_patterns.get(report_type, {})
        
        # Match headers to field patterns
        for feishu_field, excel_patterns in patterns.items():
            for header in headers:
                for pattern in excel_patterns:
                    if pattern in header or header in pattern:
                        mappings[header] = feishu_field
                        break
                if feishu_field in mappings.values():
                    break
    
    return mappings

def analyze_all_excel_files():
    """Analyze all Excel reference files"""
    logger = setup_logger()
    logger.info("Starting analysis of all Excel reference files...")
    
    # Excel files mapping
    excel_files = {
        "contract": "../excel参考/一般类合同用印审批流程-20250604141226.xlsx",
        "payment": "../excel参考/付款申请单（2023）-20250604141315.xlsx", 
        "prepayment": "../excel参考/预付款申请单（2023）-20250604141317.xlsx"
    }
    
    all_analysis = {}
    all_mappings = {}
    
    for report_type, file_path in excel_files.items():
        excel_path = Path(file_path)
        
        if excel_path.exists():
            logger.info(f"\n{'='*50}")
            logger.info(f"Analyzing {report_type.upper()} Excel file")
            logger.info(f"{'='*50}")
            
            analysis = analyze_excel_file(excel_path)
            all_analysis[report_type] = analysis
            
            if "error" not in analysis:
                # Suggest field mappings
                mappings = suggest_field_mappings(analysis, report_type)
                all_mappings[report_type] = mappings
                
                logger.info(f"Suggested field mappings for {report_type}:")
                for excel_field, feishu_field in mappings.items():
                    logger.info(f"  '{excel_field}' -> '{feishu_field}'")
            else:
                logger.error(f"Failed to analyze {report_type}: {analysis['error']}")
        else:
            logger.warning(f"Excel file not found: {excel_path}")
    
    # Save analysis results
    output_file = Path("excel_analysis_results.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump({
            "analysis": all_analysis,
            "suggested_mappings": all_mappings
        }, f, ensure_ascii=False, indent=2)
    
    logger.info(f"\nAnalysis results saved to: {output_file}")
    
    # Generate updated field mappings code
    generate_field_mappings_code(all_mappings)
    
    return all_analysis, all_mappings

def generate_field_mappings_code(mappings: Dict[str, Dict[str, str]]):
    """Generate updated field mappings code"""
    logger.info("Generating updated field mappings code...")
    
    code_lines = [
        "# Updated field mappings based on Excel analysis",
        "",
        "# Contract field mapping (合同用印审批)",
        "CONTRACT_FIELD_MAPPING = {"
    ]
    
    if "contract" in mappings:
        for excel_field, feishu_field in mappings["contract"].items():
            code_lines.append(f"    '{excel_field}': '{feishu_field}',")
    
    code_lines.extend([
        "}",
        "",
        "# Payment application field mapping (付款申请单)",
        "PAYMENT_FIELD_MAPPING = {"
    ])
    
    if "payment" in mappings:
        for excel_field, feishu_field in mappings["payment"].items():
            code_lines.append(f"    '{excel_field}': '{feishu_field}',")
    
    code_lines.extend([
        "}",
        "",
        "# Prepayment application field mapping (预付款申请单)",
        "PREPAYMENT_FIELD_MAPPING = {"
    ])
    
    if "prepayment" in mappings:
        for excel_field, feishu_field in mappings["prepayment"].items():
            code_lines.append(f"    '{excel_field}': '{feishu_field}',")
    
    code_lines.append("}")
    
    # Save to file
    output_file = Path("updated_field_mappings.py")
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(code_lines))
    
    logger.info(f"Updated field mappings code saved to: {output_file}")

def main():
    """Main function"""
    logger = setup_logger(level="INFO")

    logger.info("🔍 Excel Reference Files Analysis")
    logger.info("="*50)
    
    try:
        _, _ = analyze_all_excel_files()
        
        logger.info("\n✅ Analysis completed successfully!")
        logger.info("📁 Check the following files for results:")
        logger.info("   - excel_analysis_results.json")
        logger.info("   - updated_field_mappings.py")
        logger.info("\n💡 Next steps:")
        logger.info("   1. Review the suggested field mappings")
        logger.info("   2. Update config/field_mappings.py if needed")
        logger.info("   3. Run test_setup.py to validate configuration")
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
