#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OA system client for fetching reports and downloading Excel files
"""

import time
import requests
from typing import Dict, Optional, <PERSON>ple, Any
from dataclasses import dataclass
from pathlib import Path
import urllib.parse

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import settings, OA_REPORTS, COMPANY_CODES
from utils.logger import setup_logger
from utils.date_utils import get_date_range, get_current_timestamp
from core.cookie_manager import Cookie<PERSON>anager

@dataclass
class ExportTaskInfo:
    """Export task information"""
    status: int
    percent: str
    task_id: Optional[int]
    file_id: Optional[str]
    completed: bool
    message: Optional[str] = None

class OAClient:
    """
    OA system client for report data fetching and Excel downloads
    """
    
    def __init__(self, cookie_manager: <PERSON>ieManager):
        """
        Initialize OA client

        Args:
            cookie_manager: CookieManager instance for authentication
        """
        self.logger = setup_logger()
        self.config = settings.oa
        self.cookie_manager = cookie_manager
        self.session = requests.Session()
        self._setup_session()
    
    def _setup_session(self):
        """Setup session with headers and cookies"""
        # Get cookies from CookieManager
        cookies = self.cookie_manager.get_oa_cookies()
        self.session.cookies.update(cookies)
        
        # Set default headers
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'en,zh-CN;q=0.9,zh;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'DNT': '1',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': f'{self.config.base_url}/spa/workflow/static/index.html'
        })
        
        self.logger.info("OA session setup completed")
    
    def refresh_cookies(self):
        """Refresh session cookies"""
        if self.cookie_manager.refresh_cookies():
            cookies = self.cookie_manager.get_oa_cookies()
            self.session.cookies.clear()
            self.session.cookies.update(cookies)
            self.logger.info("OA session cookies refreshed")
        else:
            self.logger.error("Failed to refresh OA cookies")
    
    def get_report_data(self, report_type: str, date_range_days: int = 30) -> Optional[str]:
        """
        Step 1: Get report data with filter conditions
        
        Args:
            report_type: Type of report ('contract', 'payment', 'prepayment')
            date_range_days: Number of days to look back
            
        Returns:
            sessionkey string if successful, None if failed
        """
        if report_type not in OA_REPORTS:
            self.logger.error(f"Unknown report type: {report_type}")
            return None
        
        report_config = OA_REPORTS[report_type]
        start_date, end_date = get_date_range(date_range_days)
        
        url = f"{self.config.base_url}/api/workflow/standCustomReport/getReportData"
        
        # Build form data based on the API request patterns
        form_data = {
            f"{report_config['field_prefix']}_opt1": "2",
            f"{report_config['field_prefix']}_value1": COMPANY_CODES,
            "field-11_opt1": "6",
            "field-11_value1": start_date,
            "field-11_value2": end_date,
            "field-12_value1": "1108" if report_type == "payment" else ("1106" if report_type == "contract" else "1107"),
            "conditionfieldids": report_config['condition_field'],
            "templateid": str(report_config['template_id']),
            "reportParamsKey": f"{report_config['report_id']}{get_current_timestamp()}",
            "reportid": str(report_config['report_id'])
        }
        
        try:
            self.logger.info(f"Requesting {report_config['name']} data for date range {start_date} to {end_date}")
            
            response = self.session.post(
                url,
                data=form_data,
                headers={'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8'},
                timeout=self.config.timeout
            )
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    # 检查响应中是否包含sessionkey，这表示成功
                    if 'sessionkey' in result:
                        sessionkey = result['sessionkey']
                        self.logger.info(f"Successfully got report data, sessionkey: {sessionkey}")
                        return sessionkey
                    else:
                        self.logger.error(f"API returned error: {result}")
                        return None
                except ValueError as e:
                    self.logger.error(f"Failed to parse JSON response: {e}")
                    return None
            else:
                self.logger.error(f"HTTP error {response.status_code}: {response.text}")
                return None
                
        except requests.RequestException as e:
            self.logger.error(f"Network error while getting report data: {e}")
            return None
    
    def export_excel(self, report_type: str, sessionkey: str) -> bool:
        """
        Step 2: Request Excel export (使用GET方法，符合真实请求)
        
        Args:
            report_type: Type of report
            sessionkey: Session key from get_report_data (tableKey)
            
        Returns:
            True if export request successful
        """
        if report_type not in OA_REPORTS:
            self.logger.error(f"Unknown report type: {report_type}")
            return False
        
        report_config = OA_REPORTS[report_type]
        url = f"{self.config.base_url}/api/workflow/standCustomReport/exportExcel"
        
        # 使用GET参数，符合真实请求
        params = {
            'tableKey': sessionkey,
            'sortFields': '',
            'sumMainFields': '',
            'reportid': str(report_config['report_id']),
            'filename': urllib.parse.quote(f"{report_config['name']}（2023）"),
            '__random__': str(get_current_timestamp())
        }
        
        try:
            self.logger.info(f"Requesting Excel export for {report_config['name']}")
            
            response = self.session.get(url, params=params, timeout=self.config.timeout)
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('code') == 1 and result.get('data') == "异步导出":
                        self.logger.info("Excel export request successful - async export started")
                        return True
                    else:
                        self.logger.error(f"Export request failed: {result}")
                        return False
                except ValueError as e:
                    self.logger.error(f"Failed to parse export response: {e}")
                    return False
            else:
                self.logger.error(f"HTTP error during export: {response.status_code}")
                return False
                
        except requests.RequestException as e:
            self.logger.error(f"Network error during Excel export: {e}")
            return False

    def check_export_status(self, report_type: str) -> Optional[ExportTaskInfo]:
        """
        Step 3: Check export task status

        Args:
            report_type: Type of report

        Returns:
            ExportTaskInfo if successful, None if failed
        """
        if report_type not in OA_REPORTS:
            self.logger.error(f"Unknown report type: {report_type}")
            return None

        report_config = OA_REPORTS[report_type]
        url = f"{self.config.base_url}/api/workflow/standCustomReport/exportTask"

        params = {
            'reportid': str(report_config['report_id']),
            'method': 'list',
            '__random__': str(get_current_timestamp())
        }

        try:
            response = self.session.get(url, params=params, timeout=self.config.timeout)

            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('status') and 'datas' in result:
                        datas = result['datas']
                        if datas and len(datas) > 0:
                            task = datas[0]  # Get the latest task

                            return ExportTaskInfo(
                                status=task.get('status', 0),
                                percent=task.get('exportpercent', '0'),
                                task_id=task.get('id'),
                                file_id=task.get('fileid'),
                                completed=task.get('status') == 1,  # 1表示导出完成
                                message=task.get('statuscol', 'Unknown')
                            )
                        else:
                            self.logger.warning("No export tasks found")
                            return None
                    else:
                        self.logger.error(f"Failed to get export status: {result}")
                        return None
                except ValueError as e:
                    self.logger.error(f"Failed to parse status response: {e}")
                    return None
            else:
                self.logger.error(f"HTTP error checking status: {response.status_code}")
                return None

        except requests.RequestException as e:
            self.logger.error(f"Network error checking export status: {e}")
            return None

    def mark_task_downloaded(self, report_type: str, task_id: int) -> bool:
        """
        Step 3.5: Mark task as downloaded (status=2)
        
        Args:
            report_type: Type of report
            task_id: Task ID to mark as downloaded
            
        Returns:
            True if successful
        """
        if report_type not in OA_REPORTS:
            self.logger.error(f"Unknown report type: {report_type}")
            return False

        report_config = OA_REPORTS[report_type]
        url = f"{self.config.base_url}/api/workflow/standCustomReport/exportTask"

        params = {
            'reportid': str(report_config['report_id']),
            'method': 'update',
            'id': str(task_id),
            'status': '2',
            '__random__': str(get_current_timestamp())
        }

        try:
            response = self.session.get(url, params=params, timeout=self.config.timeout)

            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('status'):
                        self.logger.info(f"Task {task_id} marked as downloaded")
                        return True
                    else:
                        self.logger.error(f"Failed to mark task as downloaded: {result}")
                        return False
                except ValueError as e:
                    self.logger.error(f"Failed to parse update response: {e}")
                    return False
            else:
                self.logger.error(f"HTTP error marking task: {response.status_code}")
                return False

        except requests.RequestException as e:
            self.logger.error(f"Network error marking task: {e}")
            return False

    def download_excel_file(self, file_id: str, filename: str, output_dir: str = "downloads") -> Optional[Path]:
        """
        Step 4: Download the exported Excel file (使用正确的下载URL)

        Args:
            file_id: File ID from export task
            filename: Desired filename
            output_dir: Output directory

        Returns:
            Path to downloaded file if successful, None if failed
        """
        # 使用真实的下载URL
        url = f"{self.config.base_url}/weaver/weaver.file.FileDownload"

        params = {
            'fileid': file_id
        }

        try:
            self.logger.info(f"Downloading Excel file: {filename}")

            # 下载时不使用XMLHttpRequest头部
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'en,zh-CN;q=0.9,zh;q=0.8',
                'Accept-Encoding': 'gzip, deflate',
                'DNT': '1',
                'Upgrade-Insecure-Requests': '1',
                'Referer': f'{self.config.base_url}/spa/workflow/static/index.html'
            }

            response = self.session.get(url, params=params, headers=headers, stream=True, timeout=self.config.timeout)

            if response.status_code == 200:
                # Create output directory
                output_path = Path(output_dir)
                output_path.mkdir(parents=True, exist_ok=True)

                # Generate filename with timestamp
                timestamp = time.strftime("%Y%m%d%H%M%S")
                file_path = output_path / f"{filename}-{timestamp}.xlsx"

                # Download file
                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)

                self.logger.info(f"Excel file downloaded successfully: {file_path}")
                return file_path
            else:
                self.logger.error(f"HTTP error downloading file: {response.status_code}")
                return None

        except requests.RequestException as e:
            self.logger.error(f"Network error downloading Excel file: {e}")
            return None
        except IOError as e:
            self.logger.error(f"File I/O error: {e}")
            return None

    def full_download_process(self, report_type: str, output_dir: str = "downloads",
                            date_range_days: int = 30, max_wait_time: int = 300) -> Optional[Path]:
        """
        Complete process: get data, export, wait for completion, and download

        Args:
            report_type: Type of report to download
            output_dir: Output directory for downloaded files
            date_range_days: Number of days to look back
            max_wait_time: Maximum time to wait for export completion (seconds)

        Returns:
            Path to downloaded file if successful, None if failed
        """
        if report_type not in OA_REPORTS:
            self.logger.error(f"Unknown report type: {report_type}")
            return None

        report_config = OA_REPORTS[report_type]
        self.logger.info(f"Starting full download process for {report_config['name']}")

        # Step 1: Get report data
        sessionkey = self.get_report_data(report_type, date_range_days)
        if not sessionkey:
            self.logger.error("Failed to get report data")
            return None

        # Step 2: Request Excel export
        if not self.export_excel(report_type, sessionkey):
            self.logger.error("Failed to request Excel export")
            return None

        # Step 3: Wait for export completion
        start_time = time.time()
        while time.time() - start_time < max_wait_time:
            task_info = self.check_export_status(report_type)
            
            if task_info:
                self.logger.info(f"Export progress: {task_info.percent}% - {task_info.message}")
                
                if task_info.completed and task_info.file_id:
                    # Step 3.5: Mark as downloaded
                    if task_info.task_id:
                        self.mark_task_downloaded(report_type, task_info.task_id)
                    
                    # Step 4: Download file
                    filename = report_config['name']
                    downloaded_file = self.download_excel_file(task_info.file_id, filename, output_dir)
                    
                    if downloaded_file:
                        self.logger.info(f"Full download process completed: {downloaded_file}")
                        return downloaded_file
                    else:
                        self.logger.error("Failed to download Excel file")
                        return None
            
            # Wait before next check
            time.sleep(5)

        self.logger.error(f"Export timeout after {max_wait_time} seconds")
        return None
