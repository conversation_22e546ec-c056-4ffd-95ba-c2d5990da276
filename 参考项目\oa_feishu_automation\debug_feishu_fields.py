#!/usr/bin/env python3
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.feishu_client import FeishuClient
from config.settings import settings
from utils.logger import setup_logger

def debug_feishu_fields():
    """调试飞书字段和记录格式"""
    logger = setup_logger()
    
    try:
        # 初始化飞书客户端
        feishu_client = FeishuClient()
        
        # 获取表格字段
        table_id = settings.feishu.payment_table_id
        fields = feishu_client.get_table_fields(table_id)
        
        print("=== 飞书表格字段信息 ===")
        field_names = []
        for i, field in enumerate(fields, 1):
            print(f"{i:2d}. '{field.field_name}' (类型: {field.field_type}, ID: {field.field_id})")
            field_names.append(field.field_name)
        
        # 测试字段映射
        print("\n=== 测试字段映射 ===")
        from config.field_mappings import PREPAYMENT_FIELD_MAPPING
        
        valid_mappings = {}
        invalid_mappings = {}
        
        for excel_field, feishu_field in PREPAYMENT_FIELD_MAPPING.items():
            if feishu_field in field_names:
                valid_mappings[excel_field] = feishu_field
                print(f"✅ '{excel_field}' -> '{feishu_field}'")
            else:
                invalid_mappings[excel_field] = feishu_field
                print(f"❌ '{excel_field}' -> '{feishu_field}' (飞书中不存在)")
        
        print(f"\n有效映射: {len(valid_mappings)}")
        print(f"无效映射: {len(invalid_mappings)}")
        
        # 创建测试记录
        print("\n=== 测试记录格式 ===")
        test_record = {
            '流程编号': 'TEST001',
            '企业名称': '测试企业',
            '创建人': '测试用户',
            '创建日期': '2025-06-04',
            '当前节点': '测试节点',
            '本次支付金额': '1000.0',
            '是否有合同流程': '否'
        }
        
        print("测试记录:")
        for field, value in test_record.items():
            if field in field_names:
                print(f"  ✅ {field}: {value}")
            else:
                print(f"  ❌ {field}: {value} (字段不存在)")
        
        # 尝试验证记录格式
        print("\n=== API格式验证 ===")
        formatted_record = {'fields': test_record}
        print(f"API格式: {formatted_record}")
        
    except Exception as e:
        logger.error(f"调试过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_feishu_fields() 