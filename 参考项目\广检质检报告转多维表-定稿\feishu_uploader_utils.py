import requests
import json
import time
from retry import retry
from urllib.parse import urlparse, unquote
import os
import mimetypes

# 从配置文件导入配置
try:
    import feishu_config as config
except ImportError:
    print("错误：找不到配置文件 feishu_config.py。请确保该文件存在于同一目录下。")
    # 提供默认值或退出，具体取决于需求
    class DefaultConfig:
        APP_ID = "YOUR_APP_ID"
        APP_SECRET = "YOUR_APP_SECRET"
        AUTH_URL = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/"
        APP_TOKEN = "YOUR_APP_TOKEN"
        TABLE_ID = "YOUR_TABLE_ID"
        FEISHU_UPLOAD_PARENT_TYPE = "bitable"
    config = DefaultConfig()

# 假设的飞书OpenAPI基础URL
FEISHU_BASE_URL = "https://open.feishu.cn/open-apis/"

class HttpError(Exception):
    """自定义HTTP错误，用于retry"""
    pass

def get_tenant_access_token():
    """获取飞书tenant_access_token"""
    url = f"{FEISHU_BASE_URL}auth/v3/tenant_access_token/internal"
    headers = {
        "Content-Type": "application/json"
    }
    payload = json.dumps({
        "app_id": config.APP_ID,
        "app_secret": config.APP_SECRET
    })
    try:
        response = requests.post(url, headers=headers, data=payload, timeout=10)
        response.raise_for_status()
        response_json = response.json()
        if response_json.get("code") == 0:
            return response_json.get("tenant_access_token")
        else:
            print(f"获取tenant_access_token失败: {response_json.get('msg')}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"请求tenant_access_token失败: {e}")
        return None

@retry(HttpError, tries=3, delay=2, backoff=2)
def _make_feishu_request(method, url, headers=None, json_data=None, params=None, files=None):
    """
    通用的飞书API请求函数，包含重试逻辑。

    Args:
        method (str): HTTP方法 (GET, POST, DELETE, PUT, PATCH).
        url (str): 请求URL.
        headers (dict, optional): 请求头. Defaults to None.
        json_data (dict, optional): JSON请求体. Defaults to None.
        params (dict, optional): URL查询参数. Defaults to None.
        files (dict, optional): 用于文件上传的 multipart/form-data. Defaults to None.

    Returns:
        requests.Response: 响应对象.

    Raises:
        HttpError: 如果请求最终失败.
    """
    if not headers:
        headers = {}
    
    # 确保Authorization头部存在
    if "Authorization" not in headers:
        token = get_tenant_access_token()
        if not token:
            raise HttpError("无法获取 tenant_access_token，请求中止。")
        headers["Authorization"] = f"Bearer {token}"

    if "Content-Type" not in headers and json_data and not files: # 如果有json_data且不是文件上传，默认json
        headers["Content-Type"] = "application/json"

    try:
        if files: # 文件上传使用 data 参数，requests 会自动设置 Content-Type
            response = requests.request(method, url, headers=headers, params=params, files=files, timeout=60)
        else:
            response = requests.request(method, url, headers=headers, json=json_data, params=params, timeout=30)
        
        print(f"请求 {method} {url}: 状态码 {response.status_code}")
        if response.status_code >= 500: # 服务器错误，触发重试
            print(f"服务器错误 ({response.status_code})，正在重试... 响应: {response.text[:500]}")
            raise HttpError(f"服务器错误: {response.status_code} - {response.text[:200]}")
        
        response.raise_for_status() # 其他客户端错误 (4xx) 直接抛出，不重试
        return response
    except requests.exceptions.RequestException as e:
        print(f"请求飞书API失败: {method} {url} - {e}")
        if response is not None:
            print(f"响应内容 ({response.status_code}): {response.text[:500]}")
        raise HttpError(f"请求飞书API失败: {e}")

def get_all_records(app_token, table_id):
    """获取指定多维表的所有记录"""
    all_records = []
    page_token = None
    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records"
    
    while True:
        params = {"page_size": 500}
        if page_token:
            params["page_token"] = page_token
        
        response = _make_feishu_request("GET", url, params=params)
        data = response.json().get("data", {})
        items = data.get("items", [])
        
        # 确保 items 是一个列表
        if items:
            all_records.extend(items)
        
        page_token = data.get("page_token")
        if not data.get("has_more") or not page_token:
            break
            
    print(f"从表 {table_id} 共获取 {len(all_records)} 条记录。")
    return all_records

def batch_delete_records(app_token, table_id, record_ids):
    """批量删除多维表中的记录 (一次最多500条)"""
    if not record_ids:
        print("没有需要删除的记录。")
        return True

    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records/batch_delete"
    payload = {"records": record_ids}
    
    try:
        response = _make_feishu_request("POST", url, json_data=payload)
        # 检查响应中是否有失败的记录
        response_data = response.json()
        if response_data.get("code") == 0:
            print(f"成功删除 {len(record_ids)} 条记录。")
            failed_records = response_data.get("data", {}).get("failed_records", [])
            if failed_records:
                print(f"部分记录删除失败: {failed_records}")
                return False
            return True
        else:
            print(f"批量删除记录失败: {response_data.get('msg', '未知错误')}")
            print(f"响应: {response.text}")
            return False
    except HttpError as e:
        print(f"批量删除记录时发生HTTP错误: {e}")
        return False

def clear_all_records(app_token, table_id):
    """清空指定多维表中的所有记录"""
    print(f"开始清空表格 {app_token}/{table_id} 中的所有记录...")
    records = get_all_records(app_token, table_id)
    if not records:
        print("表格中没有记录，无需清空。")
        return True

    record_ids_to_delete = [record["record_id"] for record in records if "record_id" in record]
    
    success = True
    for i in range(0, len(record_ids_to_delete), 500):
        batch_ids = record_ids_to_delete[i:i+500]
        if not batch_delete_records(app_token, table_id, batch_ids):
            print(f"删除批次 {i//500 + 1} 失败。")
            success = False # 标记失败，但继续尝试删除其他批次
        else:
            print(f"成功删除批次 {i//500 + 1} ({len(batch_ids)}条记录)。")
        time.sleep(0.5) # API频率限制
        
    if success:
        print(f"成功清空表格 {table_id} 中的所有记录。")
    else:
        print(f"清空表格 {table_id} 过程中发生错误。部分记录可能未被删除。")
    return success

def batch_add_records(app_token, table_id, records, access_token, max_retries=3):
    """
    批量添加多维表记录
    Args:
        app_token (str): 多维表 App Token
        table_id (str): 数据表 ID
        records (list): 待添加的记录列表，每个元素是一个字典，包含'fields'
        access_token (str): tenant_access_token
        max_retries (int): 最大重试次数
    Returns:
        tuple: (bool, int, list) - 是否成功，成功添加数量，失败的记录信息
    """
    if not records:
        print("没有记录需要批量添加。")
        return True, 0, []

    url = f"{FEISHU_BASE_URL}bitable/v1/apps/{app_token}/tables/{table_id}/records/batch_create"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    success_count = 0
    failed_records_info = []

    # 飞书批量添加限制：每次最多 500 条记录
    chunk_size = 500
    for i in range(0, len(records), chunk_size):
        chunk = records[i:i + chunk_size]
        payload = json.dumps({
            "records": chunk
        })

        for attempt in range(max_retries):
            try:
                response = requests.post(url, headers=headers, data=payload, timeout=60)
                if response.status_code == 429: # 速率限制
                    retry_after = int(response.headers.get('Retry-After', 10))
                    print(f"批量添加记录遇到速率限制，等待 {retry_after} 秒后重试...")
                    time.sleep(retry_after)
                    continue
                response.raise_for_status()
                response_json = response.json()
                if response_json.get("code") == 0:
                    added_records = response_json.get("data", {}).get("records", [])
                    success_count += len(added_records)
                    print(f"成功添加 {len(added_records)} 条记录。")
                    break # 成功跳出重试循环
                else:
                    error_msg = response_json.get('msg', '未知错误')
                    print(f"批量添加记录失败 (尝试 {attempt + 1}/{max_retries}): {error_msg}")
                    if response_json.get("code") == 99991400: # 飞书特定速率限制错误码
                        time.sleep((2 ** attempt) + 1) # 指数退避
                    else:
                        # 对于非速率限制错误，记录失败信息并可能终止当前chunk的重试
                        failed_records_info.extend(chunk) # 记录失败的chunk
                        return False, success_count, failed_records_info
            except requests.exceptions.RequestException as e:
                print(f"批量添加记录请求失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                time.sleep((2 ** attempt) + 1) # 指数退避

    return True, success_count, failed_records_info

def get_bitable_records(app_token, table_id, access_token, field_names=None):
    """
    从飞书多维表分页获取所有记录，并返回以"报告编号"为键的字典。
    Args:
        app_token (str): 多维表 App Token
        table_id (str): 数据表 ID
        access_token (str): tenant_access_token
        field_names (list): 要查询的字段名称列表，例如 ['报告编号', '检测进度']
    Returns:
        dict: 以"报告编号"为键，记录对象为值的字典
    """
    records_map = {}
    page_token = None
    has_more = True
    
    while has_more:
        url = f"{FEISHU_BASE_URL}bitable/v1/apps/{app_token}/tables/{table_id}/records"
        params = {
            'page_size': 100 # 每次请求最多100条记录
        }
        if page_token:
            params['page_token'] = page_token
        if field_names:
            params['field_names'] = json.dumps(field_names) # 字段名需要JSON编码

        headers = {
            "Authorization": f"Bearer {access_token}"
        }
        
        try:
            response = requests.get(url, headers=headers, params=params, timeout=30)
            response.raise_for_status()
            response_json = response.json()
            
            if response_json.get("code") == 0:
                data = response_json.get("data", {})
                records = data.get("items", [])
                
                for record in records:
                    record_id = record.get('record_id')
                    fields = record.get('fields', {})
                    report_id = fields.get('报告编号')
                    if report_id:
                        records_map[report_id] = {'record_id': record_id, 'fields': fields}
                
                page_token = data.get('page_token')
                has_more = data.get('has_more', False)
                
            else:
                print(f"获取飞书记录失败: {response_json.get('msg')}")
                has_more = False # 停止分页
                
        except requests.exceptions.RequestException as e:
            print(f"请求飞书记录失败: {e}")
            has_more = False # 停止分页
            
    print(f"成功从飞书获取 {len(records_map)} 条现有记录。")
    return records_map

def batch_update_records(app_token, table_id, records, access_token, max_retries=3):
    """
    批量更新多维表记录
    Args:
        app_token (str): 多维表 App Token
        table_id (str): 数据表 ID
        records (list): 待更新的记录列表，每个元素是一个字典，包含'record_id'和'fields'
        access_token (str): tenant_access_token
        max_retries (int): 最大重试次数
    Returns:
        tuple: (bool, int, list) - 是否成功，成功更新数量，失败的记录信息
    """
    if not records:
        print("没有记录需要批量更新。")
        return True, 0, []

    url = f"{FEISHU_BASE_URL}bitable/v1/apps/{app_token}/tables/{table_id}/records/batch_update"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    success_count = 0
    failed_records_info = []

    # 飞书批量更新限制：每次最多 500 条记录
    chunk_size = 500
    for i in range(0, len(records), chunk_size):
        chunk = records[i:i + chunk_size]
        payload = json.dumps({
            "records": chunk
        })

        for attempt in range(max_retries):
            try:
                response = requests.post(url, headers=headers, data=payload, timeout=60)
                if response.status_code == 429: # 速率限制
                    retry_after = int(response.headers.get('Retry-After', 10))
                    print(f"批量更新记录遇到速率限制，等待 {retry_after} 秒后重试...")
                    time.sleep(retry_after)
                    continue
                response.raise_for_status()
                response_json = response.json()
                if response_json.get("code") == 0:
                    updated_records = response_json.get("data", {}).get("records", [])
                    success_count += len(updated_records)
                    print(f"成功更新 {len(updated_records)} 条记录。")
                    break # 成功跳出重试循环
                else:
                    error_msg = response_json.get('msg', '未知错误')
                    print(f"批量更新记录失败 (尝试 {attempt + 1}/{max_retries}): {error_msg}")
                    if response_json.get("code") == 99991400: # 飞书特定速率限制错误码
                        time.sleep((2 ** attempt) + 1) # 指数退避
                    else:
                        failed_records_info.extend(chunk) # 记录失败的chunk
                        return False, success_count, failed_records_info
            except requests.exceptions.RequestException as e:
                print(f"批量更新记录请求失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                time.sleep((2 ** attempt) + 1) # 指数退避

    return True, success_count, failed_records_info

def upload_file_to_feishu(file_path, parent_node=None, parent_type=config.FEISHU_UPLOAD_PARENT_TYPE):
    """
    上传文件到飞书云空间。

    Args:
        file_path (str): 本地文件路径。
        parent_node (str, optional): 父节点token。对于 explorer 类型，使用根目录或指定文件夹ID。
        parent_type (str): 父节点类型，如 "explorer"。

    Returns:
        str: 成功则返回 file_token，否则返回 None。
    """
    if not os.path.exists(file_path):
        print(f"文件未找到: {file_path}")
        return None

    file_name = os.path.basename(file_path)
    file_size = os.path.getsize(file_path)
    mime_type, _ = mimetypes.guess_type(file_path)
    if mime_type is None:
        mime_type = 'application/octet-stream' # 默认类型

    # 1. 获取上传凭证和上传点
    upload_info_url = "https://open.feishu.cn/open-apis/drive/v1/files/upload_prepare"
    payload = {
        "file_name": file_name,
        "parent_type": parent_type,
        "size": file_size
    }
    
    # 对于 explorer 类型，parent_node 是必需的
    # 如果没有提供，我们尝试使用空字符串或获取根目录
    if parent_type == "explorer":
        if not parent_node:
            # 尝试先获取根目录ID，如果失败则使用特殊值
            root_folder_id = get_root_folder_id()
            parent_node = root_folder_id if root_folder_id else ""
        payload["parent_node"] = parent_node
    else:
        # 对于其他类型，如果提供了 parent_node 则包含
        if parent_node:
            payload["parent_node"] = parent_node
    
    try:
        response = _make_feishu_request("POST", upload_info_url, json_data=payload)
        upload_data = response.json().get("data", {})
        upload_id = upload_data.get("upload_id")
        upload_url = upload_data.get("upload_url") # 这是预签名URL，不需要 Authorization
        
        # 注意：返回的 file_token 在不同阶段可能有不同的名称
        file_token_for_finish = upload_data.get("file_token")

        if not all([upload_id, upload_url]):
            print(f"获取文件上传凭证失败，缺少必要信息: upload_id={upload_id}, upload_url={upload_url}")
            print(f"完整响应: {response.text}")
            return None

        print(f"获取上传凭证成功: upload_id={upload_id}")

        # 2. 分片上传（这里简化为单块上传）
        headers_for_upload = {} # 通常不需要 Authorization for presigned URL
            
        with open(file_path, 'rb') as f:
            # 使用 multipart/form-data 上传
            response_upload = requests.post(upload_url, files={'file': (file_name, f, mime_type)}, headers=headers_for_upload, timeout=120)

        response_upload.raise_for_status() # 检查上传是否成功
        print(f"文件块上传到预签名URL成功，状态码 {response_upload.status_code}")

        # 3. 完成上传
        finish_url = "https://open.feishu.cn/open-apis/drive/v1/files/upload_finish"
        finish_payload = {
            "upload_id": upload_id,
            "block_num": 1 # 假设只有一块
        }
        
        response_finish = _make_feishu_request("POST", finish_url, json_data=finish_payload)
        
        finished_data = response_finish.json().get("data", {})
        file_token = finished_data.get("file_token")

        if file_token:
            print(f"文件 {file_name} 上传完成，file_token: {file_token}")
            return file_token
        else:
            print(f"完成文件上传失败，未获取到 file_token")
            print(f"完成上传响应: {response_finish.text}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"上传文件 {file_name} 过程中发生请求错误: {e}")
        if 'response_upload' in locals() and response_upload is not None:
            print(f"上传块响应 ({response_upload.status_code}): {response_upload.text[:500]}")
        return None
    except HttpError as e: # 来自 _make_feishu_request
        print(f"上传文件 {file_name} 过程中发生飞书API错误: {e}")
        return None
    except Exception as e:
        print(f"上传文件 {file_name} 过程中发生未知错误: {e}")
        return None

def get_root_folder_id():
    """获取用户云盘根目录ID"""
    try:
        # 尝试获取根目录信息
        url = "https://open.feishu.cn/open-apis/drive/v1/files"
        params = {"parent_token": ""}  # 空值获取根目录
        
        response = _make_feishu_request("GET", url, params=params)
        data = response.json().get("data", {})
        
        # 通常根目录的 token 可以通过其他API获取，这里我们返回空字符串作为fallback
        return ""
    except Exception as e:
        print(f"获取根目录ID失败: {e}")
        return ""

if __name__ == '__main__':
    # 测试代码 (需要配置 feishu_config.py)
    print("正在测试飞书工具函数...")

    # 覆盖默认测试用的 app_token 和 table_id (如果需要)
    # config.APP_TOKEN = "你的测试app_token"
    # config.TABLE_ID = "你的测试table_id"
    
    if config.APP_ID == "YOUR_APP_ID":
        print("请在 feishu_config.py 中配置您的飞书应用信息后再运行测试。")
    else:
        # 1. 测试获取token
        token = get_tenant_access_token()
        print(f"获取到的 Tenant Access Token (前10位): {token[:10] if token else '失败'}")

        if token:
            # # 2. 测试清空记录 (危险操作，请确保是测试表)
            # print("\n测试清空记录...")
            # # clear_all_records(config.APP_TOKEN, config.TABLE_ID) # 默认注释掉以防误操作

            # 3. 测试添加记录
            print("\n测试添加记录...")
            sample_records_to_add = [
                {"fields": {"文本字段": "测试文本1", "数字字段": 100}},
                {"fields": {"文本字段": "测试文本2", "数字字段": 200}}
            ]
            # 请确保 "文本字段" 和 "数字字段" 在你的测试表中存在
            # success_add, added_count, failed_info = batch_add_records(config.APP_TOKEN, config.TABLE_ID, sample_records_to_add)
            # print(f"添加完成，成功: {success_add}, 添加数量: {added_count}")
            # if failed_info:
            #     print(f"失败的记录信息: {failed_info}")

            # 4. 测试获取记录
            print("\n测试获取所有记录...")
            all_recs = get_all_records(config.APP_TOKEN, config.TABLE_ID)
            if all_recs:
                print(f"获取到 {len(all_recs)} 条记录。第一条记录 (部分):")
                print(json.dumps(all_recs[0]['fields'], ensure_ascii=False, indent=2))


            # 5. 测试文件上传 (创建一个临时文件)
            print("\n测试文件上传...")
            temp_file_path = "temp_test_file.txt"
            with open(temp_file_path, "w") as f:
                f.write("这是一个飞书文件上传测试。")
            
            # parent_node 通常是 app_token (PktpbXFUVaUvVBsbGB1c8BBTnob)
            # file_token = upload_file_to_feishu(temp_file_path, parent_node=config.APP_TOKEN, parent_type="bitable")
            # if file_token:
            #     print(f"文件上传成功，File Token: {file_token}")
                
            #     # 测试将此文件 token 添加到多维表格的附件字段
            #     # 假设你的表格有一个名为 "附件测试" 的附件字段
            #     record_with_attachment = [{
            #         "fields": {
            #             "文本字段": "记录带附件",
            #             "附件测试": [{"file_token": file_token}] # 附件字段是列表
            #         }
            #     }]
            #     # batch_add_records(config.APP_TOKEN, config.TABLE_ID, record_with_attachment)
            # else:
            #     print("文件上传失败。")
            
            # if os.path.exists(temp_file_path):
            #     os.remove(temp_file_path)
            print("请取消注释 feishu_uploader_utils.py 末尾的测试代码并确保字段名正确，以进行完整测试。")

        print("\n飞书工具函数测试结束。") 