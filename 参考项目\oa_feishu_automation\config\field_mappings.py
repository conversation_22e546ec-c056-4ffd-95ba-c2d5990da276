#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Field mappings for OA to Feishu data transformation
"""

# Contract field mapping (合同用印审批)
# 映射：Excel列名 -> 飞书字段名 (基于实际Excel和飞书字段结构)
CONTRACT_FIELD_MAPPING = {
    # 基础字段
    '流程编号': '流程编号',
    '创建人': '创建人',
    '我方公司': '我方公司',
    '对方公司': '对方公司',
    '合同金额（标的金额）': '合同金额（标的金额）',
    '收/付款阶段': '收|付款阶段',
    '收/付款条件': '收|付款条件',
    '收/付款金额': '收|付款金额',
    '收/付款比例': '收|付款比例',
    '招标比价编号': '招标比价编号',
    '主要内容': '主要内容',
    '流程审批类型': '流程审批类型',
    '创建日期': '创建日期',
    '合同类型': '合同类型',
    '当前节点': '当前节点',
    '签字意见': '签字意见',
    '开始日期': '开始日期',
    '截至日期': '截至日期',
    '是否框架合同': '是否框架合同',
    '送审部门': '送审部门',
    '合同名称': '合同名称',
    
    # 可选字段
    '是否关联原合同': '是否关联原合同',
    '原合同': '原合同',
    '原合同名称': '原合同名称',
    '是否需要招标比价': '是否需要招标比价',
    '合同审核稿上传': '合同审核稿上传',
    '资质上传': '资质上传',
    '双方盖章后合同上传': '双方盖章后合同上传',
    '固定期间': '固定期间',
    '合同期限': '合同期限',
}

# Payment application field mapping (付款申请单)
# 映射：Excel列名 -> 飞书字段名 (与预付款申请单相同的飞书表格)
PAYMENT_FIELD_MAPPING = {
    # 基础必需字段 - 总是存在
    '流程编号': '流程编号',
    '合同规定付款条件': '合同规定付款条件',
    '税额': '税额',
    '去税金额': '去税金额',
    '附件': '附件',
    '创建日期': '创建日期',
    '当前节点': '当前节点',
    '签字意见': '签字意见',
    '费用类型': '费用类型',
    '供应商名称': '供应商名称',
    '是否有合同流程': '是否有合同流程',
    '企业名称': '企业名称',
    '款项用途': '款项用途',
    '本次支付金额': '本次支付金额',
    '专票税额（不是专票填写0）': '专票税额（不是专票填写0）',
    '去税总金额': '去税总金额',
    '创建人': '创建人',
    '费用所属部门': '费用所属部门',
    '相关合同': '相关合同',
    '创建人所属部门': '创建人所属部门',
    '金额': '金额',
    '选择发票': '选择发票',
    '发票类型': '发票类型',
    '税率': '税率',
}

# Prepayment application field mapping (预付款申请单)
# 映射：Excel列名 -> 飞书字段名 (只映射确实存在且有数据的字段)
PREPAYMENT_FIELD_MAPPING = {
    # 基础必需字段 - 总是存在
    '流程编号': '流程编号',
    '企业名称': '企业名称', 
    '合同规定付款条件': '合同规定付款条件',
    '创建人': '创建人',
    '创建日期': '创建日期',
    '当前节点': '当前节点', 
    '本次支付金额': '本次支付金额',
    '附件': '附件',
    '签字意见': '签字意见',
    '费用类型': '费用类型',
    '供应商名称': '供应商名称',
    '是否有合同流程': '是否有合同流程',
    '专票税额（不是专票填写0）': '专票税额（不是专票填写0）',
    '去税总金额': '去税总金额',
    '款项用途': '款项用途',
    '费用所属部门': '费用所属部门',
    '创建人所属部门': '创建人所属部门',
    
    # 可选字段 - 只在数据存在时映射
    '发票类型': '发票类型',
    '金额': '金额',
    '税额': '税额',
    '税率': '税率',
    '去税金额': '去税金额',
    '相关合同': '相关合同',
    '选择发票': '选择发票',
}

# 合同用印审批字段类型定义 (基于实际飞书表格字段类型)
CONTRACT_FIELD_TYPES = {
    '流程编号': 1,                          # 文本
    '创建人': 1,                            # 文本
    '我方公司': 3,                          # 单选
    '对方公司': 1,                          # 文本
    '合同金额（标的金额）': 1,              # 文本
    '收|付款阶段': 3,                       # 单选
    '收|付款条件': 1,                       # 文本
    '收|付款金额': 1,                       # 文本
    '收|付款比例': 1,                       # 文本
    '招标比价编号': 1,                      # 文本
    '主要内容': 1,                          # 文本
    '流程审批类型': 3,                      # 单选
    '创建日期': 1,                          # 文本
    '合同类型': 3,                          # 单选
    '当前节点': 3,                          # 单选
    '签字意见': 1,                          # 文本
    '开始日期': 1,                          # 文本
    '截至日期': 1,                          # 文本
    '是否框架合同': 3,                      # 单选
    '送审部门': 1,                          # 文本
    '合同名称': 1,                          # 文本
    '是否关联原合同': 3,                    # 单选
    '原合同': 1,                            # 文本
    '原合同名称': 1,                        # 文本
    '是否需要招标比价': 3,                  # 单选
    '合同审核稿上传': 1,                    # 文本
    '资质上传': 1,                          # 文本
    '双方盖章后合同上传': 1,                # 文本
    '固定期间': 1,                          # 文本
    '合同期限': 1,                          # 文本
}

# 付款申请单字段类型定义 (与预付款申请单相同，使用同一张飞书表格)
PAYMENT_FIELD_TYPES = {
    '流程编号': 1,                          # 文本
    '合同规定付款条件': 1,                  # 文本
    '税额': 1,                              # 文本
    '去税金额': 1,                          # 文本
    '附件': 1,                              # 文本
    '创建日期': 1,                          # 文本
    '当前节点': 3,                          # 单选
    '签字意见': 1,                          # 文本
    '费用类型': 1,                          # 文本
    '供应商名称': 1,                        # 文本
    '是否有合同流程': 3,                    # 单选
    '企业名称': 3,                          # 单选
    '款项用途': 1,                          # 文本
    '本次支付金额': 1,                      # 文本
    '专票税额（不是专票填写0）': 1,         # 文本
    '去税总金额': 1,                        # 文本
    '创建人': 1,                            # 文本
    '费用所属部门': 1,                      # 文本
    '相关合同': 1,                          # 文本
    '创建人所属部门': 1,                    # 文本
    '金额': 1,                              # 文本
    '选择发票': 1,                          # 文本
    '发票类型': 3,                          # 单选
    '税率': 1,                              # 文本
}

# 预付款申请单字段类型定义 (基于实际飞书表格字段类型)
PREPAYMENT_FIELD_TYPES = {
    '流程编号': 1,                          # 文本
    '合同规定付款条件': 1,                  # 文本
    '税额': 1,                              # 文本 (实际是数字，但飞书设置为文本)
    '去税金额': 1,                          # 文本
    '附件': 1,                              # 文本
    '创建日期': 1,                          # 文本
    '当前节点': 3,                          # 单选
    '签字意见': 1,                          # 文本
    '费用类型': 1,                          # 文本
    '供应商名称': 1,                        # 文本
    '是否有合同流程': 3,                    # 单选
    '企业名称': 3,                          # 单选
    '款项用途': 1,                          # 文本
    '本次支付金额': 1,                      # 文本
    '专票税额（不是专票填写0）': 1,         # 文本
    '去税总金额': 1,                        # 文本
    '创建人': 1,                            # 文本
    '费用所属部门': 1,                      # 文本
    '相关合同': 1,                          # 文本
    '创建人所属部门': 1,                    # 文本
    '金额': 1,                              # 文本
    '选择发票': 1,                          # 文本
    '发票类型': 3,                          # 单选
    '税率': 1,                              # 文本
}

# 合同用印审批字段格式化配置
CONTRACT_FIELD_FORMATTERS = {
    # 文本字段 - 确保非空字符串
    '流程编号': lambda x: str(x).strip() if x is not None else '',
    '创建人': lambda x: str(x).strip() if x is not None else '',
    '我方公司': lambda x: str(x).strip() if x is not None else '',
    '对方公司': lambda x: str(x).strip() if x is not None else '',
    '合同金额（标的金额）': lambda x: str(x) if x is not None and str(x).strip() else '',
    '收|付款阶段': lambda x: str(x).strip() if x is not None else '',
    '收|付款条件': lambda x: str(x).strip() if x is not None else '',
    '收|付款金额': lambda x: str(x) if x is not None and str(x).strip() else '',
    '收|付款比例': lambda x: str(x) if x is not None and str(x).strip() else '',
    '招标比价编号': lambda x: str(x).strip() if x is not None else '',
    '主要内容': lambda x: str(x).strip() if x is not None else '',
    '流程审批类型': lambda x: str(x).strip() if x is not None else '',
    '创建日期': lambda x: str(x).strip() if x is not None else '',
    '合同类型': lambda x: str(x).strip() if x is not None else '',
    '当前节点': lambda x: str(x).strip() if x is not None else '',
    '签字意见': lambda x: str(x).strip() if x is not None else '',
    '开始日期': lambda x: str(x).strip() if x is not None else '',
    '截至日期': lambda x: str(x).strip() if x is not None else '',
    '是否框架合同': lambda x: str(x).strip() if x is not None else '',
    '送审部门': lambda x: str(x).strip() if x is not None else '',
    '合同名称': lambda x: str(x).strip() if x is not None else '',
    '是否关联原合同': lambda x: str(x).strip() if x is not None else '',
    '原合同': lambda x: str(x).strip() if x is not None else '',
    '原合同名称': lambda x: str(x).strip() if x is not None else '',
    '是否需要招标比价': lambda x: str(x).strip() if x is not None else '',
    '合同审核稿上传': lambda x: str(x).strip() if x is not None else '',
    '资质上传': lambda x: str(x).strip() if x is not None else '',
    '双方盖章后合同上传': lambda x: str(x).strip() if x is not None else '',
    '固定期间': lambda x: str(x).strip() if x is not None else '',
    '合同期限': lambda x: str(x).strip() if x is not None else '',
}

# 付款申请单字段格式化配置 (与预付款申请单相同)
PAYMENT_FIELD_FORMATTERS = {
    # 数字字段 - 确保转换为字符串
    '金额': lambda x: str(x) if x is not None and str(x).strip() else '',
    '税额': lambda x: str(x) if x is not None and str(x).strip() else '',
    '税率': lambda x: str(x) if x is not None and str(x).strip() else '',
    '去税金额': lambda x: str(x) if x is not None and str(x).strip() else '',
    '本次支付金额': lambda x: str(x) if x is not None and str(x).strip() else '',
    '专票税额（不是专票填写0）': lambda x: str(x) if x is not None and str(x).strip() else '',
    '去税总金额': lambda x: str(x) if x is not None and str(x).strip() else '',
    
    # 文本字段 - 确保非空字符串
    '流程编号': lambda x: str(x).strip() if x is not None else '',
    '企业名称': lambda x: str(x).strip() if x is not None else '',
    '发票类型': lambda x: str(x).strip() if x is not None else '',
    '合同规定付款条件': lambda x: str(x).strip() if x is not None else '',
    '创建人': lambda x: str(x).strip() if x is not None else '',
    '创建日期': lambda x: str(x).strip() if x is not None else '',
    '当前节点': lambda x: str(x).strip() if x is not None else '',
    '附件': lambda x: str(x).strip() if x is not None else '',
    '签字意见': lambda x: str(x).strip() if x is not None else '',
    '费用类型': lambda x: str(x).strip() if x is not None else '',
    '供应商名称': lambda x: str(x).strip() if x is not None else '',
    '是否有合同流程': lambda x: str(x).strip() if x is not None else '',
    '款项用途': lambda x: str(x).strip() if x is not None else '',
    '费用所属部门': lambda x: str(x).strip() if x is not None else '',
    '相关合同': lambda x: str(x).strip() if x is not None else '',
    '创建人所属部门': lambda x: str(x).strip() if x is not None else '',
    '选择发票': lambda x: str(x).strip() if x is not None else '',
}

# 预付款申请单字段格式化配置
PREPAYMENT_FIELD_FORMATTERS = {
    # 数字字段 - 确保转换为字符串
    '金额': lambda x: str(x) if x is not None and str(x).strip() else '',
    '税额': lambda x: str(x) if x is not None and str(x).strip() else '',
    '税率': lambda x: str(x) if x is not None and str(x).strip() else '',
    '去税金额': lambda x: str(x) if x is not None and str(x).strip() else '',
    '本次支付金额': lambda x: str(x) if x is not None and str(x).strip() else '',
    '专票税额（不是专票填写0）': lambda x: str(x) if x is not None and str(x).strip() else '',
    '去税总金额': lambda x: str(x) if x is not None and str(x).strip() else '',
    
    # 文本字段 - 确保非空字符串
    '流程编号': lambda x: str(x).strip() if x is not None else '',
    '企业名称': lambda x: str(x).strip() if x is not None else '',
    '发票类型': lambda x: str(x).strip() if x is not None else '',
    '合同规定付款条件': lambda x: str(x).strip() if x is not None else '',
    '创建人': lambda x: str(x).strip() if x is not None else '',
    '创建日期': lambda x: str(x).strip() if x is not None else '',
    '当前节点': lambda x: str(x).strip() if x is not None else '',
    '附件': lambda x: str(x).strip() if x is not None else '',
    '签字意见': lambda x: str(x).strip() if x is not None else '',
    '费用类型': lambda x: str(x).strip() if x is not None else '',
    '供应商名称': lambda x: str(x).strip() if x is not None else '',
    '是否有合同流程': lambda x: str(x).strip() if x is not None else '',
    '款项用途': lambda x: str(x).strip() if x is not None else '',
    '费用所属部门': lambda x: str(x).strip() if x is not None else '',
    '相关合同': lambda x: str(x).strip() if x is not None else '',
    '创建人所属部门': lambda x: str(x).strip() if x is not None else '',
    '选择发票': lambda x: str(x).strip() if x is not None else '',
}

# Mapping configuration for each report type
FIELD_MAPPINGS = {
    'contract': CONTRACT_FIELD_MAPPING,
    'payment': PAYMENT_FIELD_MAPPING,
    'prepayment': PREPAYMENT_FIELD_MAPPING
}

# Field types configuration for each report type
FIELD_TYPES = {
    'contract': CONTRACT_FIELD_TYPES,
    'payment': PAYMENT_FIELD_TYPES,
    'prepayment': PREPAYMENT_FIELD_TYPES
}

# Field formatters configuration for each report type
FIELD_FORMATTERS = {
    'contract': CONTRACT_FIELD_FORMATTERS,
    'payment': PAYMENT_FIELD_FORMATTERS,
    'prepayment': PREPAYMENT_FIELD_FORMATTERS
}

# Common field types for Feishu
FEISHU_FIELD_TYPES = {
    'text': 1,          # 文本
    'number': 2,        # 数字
    'select': 3,        # 单选
    'multi_select': 4,  # 多选
    'date': 5,          # 日期
    'checkbox': 7,      # 复选框
    'user': 11,         # 人员
    'phone': 13,        # 电话号码
    'url': 15,          # 超链接
    'attachment': 17,   # 附件
    'barcode': 20,      # 条码
    'progress': 21,     # 进度
    'currency': 22,     # 货币
    'rating': 23,       # 评分
    'created_time': 1001,  # 创建时间
    'modified_time': 1002, # 最后更新时间
    'created_user': 1003,  # 创建人
    'modified_user': 1004  # 修改人
}
