#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云图客户端 - 用于获取云图数据
"""

import requests
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from urllib.parse import urlencode

from ..config import settings, YUNTU_REQUEST_PARAMS
from ..utils import setup_logger
from .cookie_manager import CookieManager

class YuntuClient:
    """
    云图客户端 - 获取触点效果分析数据
    """
    
    def __init__(self):
        """初始化云图客户端"""
        self.logger = setup_logger()
        self.config = settings.yuntu
        self.cookie_manager = CookieManager()
        self.session = requests.Session()
        
        # 设置默认请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            'Origin': self.config.base_url,
            'Referer': f'{self.config.base_url}/yuntu_brand/ecom/evaluation_brand/history/distribution',
            'Accept-Language': 'en,zh-CN;q=0.9,zh;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br, zstd'
        })
    
    def _get_cookies(self) -> Dict[str, str]:
        """获取云图域名的cookie"""
        return self.cookie_manager.get_cookies_for_requests(self.config.domain)
    
    def _build_request_params(self, start_date: str, end_date: str, 
                             advertiser_id: Optional[str] = None) -> Dict[str, Any]:
        """
        构建请求参数
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            advertiser_id: 广告主ID
            
        Returns:
            请求参数字典
        """
        params = YUNTU_REQUEST_PARAMS.copy()
        params['start_date'] = start_date
        params['end_date'] = end_date
        
        # 如果提供了广告主ID，可以在这里设置
        # 目前使用配置中的默认值
        
        return params
    
    def get_trigger_insight_data(self, start_date: str, end_date: str,
                                advertiser_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        获取触点洞察效果分析数据
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            advertiser_id: 广告主ID
            
        Returns:
            API响应数据
        """
        # 构建URL
        url = f"{self.config.base_url}{self.config.api_endpoint}"
        
        # 添加查询参数
        if advertiser_id:
            url += f"?aadvid={advertiser_id}"
        else:
            # 使用默认的广告主ID
            url += "?aadvid=1731407470252045"
        
        # 构建请求体
        request_data = self._build_request_params(start_date, end_date, advertiser_id)
        
        # 获取cookie
        cookies = self._get_cookies()
        if not cookies:
            self.logger.error("未获取到有效的cookie")
            return None
        
        try:
            self.logger.info(f"请求云图数据: {start_date} 到 {end_date}")
            self.logger.debug(f"请求URL: {url}")
            self.logger.debug(f"请求参数: {json.dumps(request_data, ensure_ascii=False)}")
            
            response = self.session.post(
                url,
                json=request_data,
                cookies=cookies,
                timeout=self.config.timeout
            )
            
            self.logger.info(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('code') == 0:
                    self.logger.info("成功获取云图数据")
                    return result.get('data', {})
                else:
                    self.logger.error(f"API返回错误: {result}")
                    return None
            else:
                self.logger.error(f"HTTP错误 {response.status_code}: {response.text}")
                return None
                
        except requests.RequestException as e:
            self.logger.error(f"网络请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {e}")
            return None
    
    def get_month_data(self, year: int, month: int) -> Optional[Dict[str, Any]]:
        """
        获取指定月份的数据

        Args:
            year: 年份
            month: 月份

        Returns:
            API响应数据
        """
        # 使用月份的最后一天作为请求日期
        # 因为云图API的日期实际代表的是整个月的数据
        from calendar import monthrange
        last_day = monthrange(year, month)[1]
        date_str = f"{year}-{month:02d}-{last_day:02d}"

        self.logger.info(f"获取 {year}年{month}月 的数据，使用日期: {date_str}")
        return self.get_trigger_insight_data(date_str, date_str)

    def get_historical_data(self, start_year: int = 2024, start_month: int = 1,
                           max_months: int = 24) -> List[Tuple[str, Dict[str, Any]]]:
        """
        获取历史数据

        Args:
            start_year: 开始年份
            start_month: 开始月份
            max_months: 最多获取多少个月

        Returns:
            (数据周期, 数据)的列表
        """
        historical_data = []
        current_date = datetime.now()
        current_year = current_date.year
        current_month = current_date.month

        # 生成月份列表
        months_to_fetch = []
        year, month = start_year, start_month

        for _ in range(max_months):
            # 不获取未来的月份
            if year > current_year or (year == current_year and month > current_month):
                break

            months_to_fetch.append((year, month))

            # 下一个月
            month += 1
            if month > 12:
                month = 1
                year += 1

        self.logger.info(f"准备获取 {len(months_to_fetch)} 个月的历史数据")

        for year, month in months_to_fetch:
            try:
                data_period = f"{year}/{month:02d}"
                self.logger.info(f"获取 {data_period} 的数据...")

                data = self.get_month_data(year, month)
                if data:
                    historical_data.append((data_period, data))
                    self.logger.info(f"成功获取 {data_period} 的数据")
                else:
                    self.logger.warning(f"获取 {data_period} 的数据失败")

                # 避免请求过于频繁
                time.sleep(1)

            except Exception as e:
                self.logger.error(f"获取 {year}/{month:02d} 数据时出错: {e}")
                continue

        self.logger.info(f"历史数据获取完成，共获取 {len(historical_data)} 个月的数据")
        return historical_data

    def get_latest_month_data(self) -> Optional[Tuple[str, Dict[str, Any]]]:
        """
        获取最新月份的数据（当前月或上个月）

        Returns:
            (数据周期, 数据)或None
        """
        current_date = datetime.now()

        # 先尝试当前月
        current_period = f"{current_date.year}/{current_date.month:02d}"
        data = self.get_month_data(current_date.year, current_date.month)

        if data and data.get('distribution_list'):
            return (current_period, data)

        # 如果当前月没有数据，尝试上个月
        if current_date.month == 1:
            prev_year = current_date.year - 1
            prev_month = 12
        else:
            prev_year = current_date.year
            prev_month = current_date.month - 1

        prev_period = f"{prev_year}/{prev_month:02d}"
        data = self.get_month_data(prev_year, prev_month)

        if data and data.get('distribution_list'):
            return (prev_period, data)

        return None
    
    def test_connection(self) -> bool:
        """
        测试云图API连接

        Returns:
            连接是否成功
        """
        try:
            self.logger.info("测试云图API连接...")

            # 测试获取最新月份的数据
            result = self.get_latest_month_data()

            if result:
                data_period, data = result
                distribution_list = data.get('distribution_list', [])
                self.logger.info(f"连接成功! 获取到 {data_period} 的 {len(distribution_list)} 个触点数据")
                return True
            else:
                self.logger.error("连接失败: 未获取到数据")
                return False

        except Exception as e:
            self.logger.error(f"连接测试失败: {e}")
            return False
