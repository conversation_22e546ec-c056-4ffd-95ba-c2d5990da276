#!/usr/bin/env python3
import pandas as pd
import sys
from pathlib import Path

def debug_excel_processing(file_path):
    """调试Excel处理过程"""
    print(f"=== 调试Excel文件: {file_path} ===")
    
    # 1. 读取原始Excel
    print("\n1. 原始Excel结构:")
    df_raw = pd.read_excel(file_path)
    print(f"形状: {df_raw.shape}")
    print(f"列名: {list(df_raw.columns)}")
    
    # 2. 检查空列
    print("\n2. 空列分析:")
    for col in df_raw.columns:
        null_count = df_raw[col].isna().sum()
        empty_count = (df_raw[col] == "").sum() if df_raw[col].dtype == 'object' else 0
        total_empty = null_count + empty_count
        print(f"  {col}: {total_empty}/{len(df_raw)} 空值")
    
    # 3. 应用清理逻辑
    print("\n3. 应用清理后:")
    df_cleaned = df_raw.dropna(how='all').dropna(axis=1, how='all')
    print(f"形状: {df_cleaned.shape}")
    print(f"列名: {list(df_cleaned.columns)}")
    
    # 4. 被删除的列
    removed_cols = set(df_raw.columns) - set(df_cleaned.columns)
    if removed_cols:
        print(f"\n4. 被删除的列: {list(removed_cols)}")
        for col in removed_cols:
            print(f"  {col}: 全部为空")
    else:
        print("\n4. 没有列被删除")
    
    # 5. 检查前几行数据
    print("\n5. 前3行数据:")
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', None)
    print(df_cleaned.head(3))

if __name__ == "__main__":
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        file_path = "downloads/预付款申请单-20250604163446.xlsx"
    
    debug_excel_processing(file_path) 