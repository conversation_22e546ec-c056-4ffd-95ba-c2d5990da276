# GTTC 报告数据自动同步到飞书多维表

## 📋 项目简介

这是一个自动化工具，用于从广东省测试技术研究所(GTTC)网站抓取质检报告数据，并自动同步到飞书(Lark)多维表格中。支持附件下载和上传，实现完整的数据同步流程。

## ✨ 主要功能

- 🔄 **自动数据同步**: 定期从 GTTC 网站抓取最新报告数据
- 📁 **附件管理**: 自动下载报告附件并上传到飞书
- 🔗 **增量更新**: 智能识别新增和变更数据，避免重复处理
- ⏰ **定时执行**: 支持每小时自动执行同步任务
- 🛡️ **容错机制**: 包含重试逻辑和错误处理
- 📊 **详细统计**: 提供同步结果的详细统计信息
- 🐳 **Docker 部署**: 支持容器化部署，便于管理和扩展

## 📂 项目结构

```
广检质检报告转多维表-定稿/
├── gttc_to_feishu_uploader.py    # 主程序入口
├── gttc_scraper.py               # GTTC 网站数据抓取模块
├── feishu_uploader_utils.py      # 飞书 API 工具类
├── feishu_config.py              # 配置文件
├── scheduler.py                  # 定时任务调度器
├── requirements.txt              # Python 依赖包
├── Dockerfile                    # Docker 构建文件
├── .dockerignore                 # Docker 忽略文件
├── .env                         # 环境变量配置文件
└── README.md                    # 项目说明文档
```

## 🔧 环境配置

### 1. 环境变量设置

创建 `.env` 文件并配置以下参数：

```env
# 飞书应用配置
FEISHU_APP_ID=your_app_id_here
FEISHU_APP_SECRET=your_app_secret_here

# 飞书多维表配置
FEISHU_APP_TOKEN=your_app_token_here
FEISHU_TABLE_ID=your_table_id_here
```

### 2. 获取飞书配置信息

#### 获取 APP_ID 和 APP_SECRET:
1. 访问 [飞书开放平台](https://open.feishu.cn/)
2. 创建企业自建应用
3. 在"凭证与基础信息"页面获取 App ID 和 App Secret

#### 获取 APP_TOKEN 和 TABLE_ID:
1. 打开飞书多维表格
2. 从 URL 中提取：`https://example.feishu.cn/base/{APP_TOKEN}?table={TABLE_ID}`

#### 配置应用权限:
确保应用具有以下权限：
- `bitable:app` - 查看、评论、编辑和管理多维表格
- `drive:drive` - 查看、评论、编辑和管理云空间中的文件

## 🚀 部署方式

### 方式一：Docker 部署（推荐）

#### 1. 使用 Zeabur 部署
1. 将项目推送到 GitHub
2. 在 [Zeabur](https://zeabur.com) 创建新项目
3. 连接 GitHub 仓库
4. 配置环境变量
5. 自动部署

#### 2. 使用其他容器平台
- **Railway**: 支持 GitHub 自动部署
- **Render**: 免费容器托管
- **Google Cloud Run**: 按使用量计费
- **Heroku**: 经典容器平台

#### 3. 本地 Docker 运行
```bash
# 构建镜像
docker build -t gttc-feishu-sync .

# 运行容器
docker run --env-file .env gttc-feishu-sync
```

### 方式二：本地 Python 运行

```bash
# 安装依赖
pip install -r requirements.txt

# 单次执行
python gttc_to_feishu_uploader.py

# 定时执行
python scheduler.py
```

## ⚙️ 字段映射配置

在 `feishu_config.py` 中配置 GTTC 字段到飞书字段的映射：

```python
FIELD_MAPPING = {
    '报告编号': '报告编号',
    '委托单位': '委托单位',
    '商标': '商标',
    '面料编号': '面料编号',
    # ... 更多字段映射
    '受理单下载链接': '受理单附件',
    '缴费通知单下载链接': '缴费通知单附件',
    '报告书下载链接': '报告书附件'
}
```

## 📊 同步策略

### 数据抓取范围
- 默认抓取最近 30 天的数据
- 可在代码中调整日期范围

### 增量更新逻辑
1. **新记录**: 直接添加到飞书表格
2. **现有记录**: 比较字段变化，仅更新有变化的记录
3. **附件处理**: 
   - 如果飞书中已有附件，保留现有附件
   - 如果 GTTC 有新附件，下载并上传到飞书
   - 如果 GTTC 链接失效，清空飞书对应字段

### 附件下载规则
- **受理单**: 始终尝试下载
- **缴费通知单**: 始终尝试下载  
- **报告书**: 仅当检测进度为"已出证"且报告书状态为"可下载"时下载

## 📈 执行统计

程序会输出详细的执行统计信息：

```
📊 报告书处理统计 (基于 X 条实际处理的报告):
  🎯 真正可下载 (已出证且可下载): X 条
  📥 成功下载报告书: X 条
  ⚠️  数据不一致 (如已出证但链接无效): X 条
  ⏳ 报告书处理中 (未出证等): X 条
  📈 报告书下载成功率: X/X (XX.X%)
```

## 🔧 定时任务配置

### 修改执行频率

编辑 `scheduler.py` 中的时间间隔：

```python
# 每小时执行 (默认)
time.sleep(3600)

# 每30分钟执行
time.sleep(1800)

# 每10分钟执行
time.sleep(600)
```

### 监控和日志

- 容器部署时可通过平台控制台查看日志
- 本地运行时直接在终端查看输出
- 包含时间戳和详细的执行信息

## 🛠️ 故障排除

### 常见问题

1. **获取 access_token 失败**
   - 检查 APP_ID 和 APP_SECRET 是否正确
   - 确认应用权限配置是否完整

2. **多维表格操作失败**
   - 验证 APP_TOKEN 和 TABLE_ID 是否正确
   - 确认应用已添加到多维表格

3. **附件上传失败**
   - 检查网络连接
   - 确认文件大小未超过限制
   - 验证飞书存储权限

4. **数据抓取失败**
   - 检查 GTTC 网站是否可访问
   - 验证网站结构是否发生变化

### 调试模式

如需调试，可以修改代码中的日期范围，只抓取少量数据进行测试：

```python
# 在 main() 函数中修改
start_date = today - timedelta(days=1)  # 只抓取最近1天
```

## 📝 开发说明

### 技术栈
- **Python 3.9+**: 主要开发语言
- **requests**: HTTP 请求处理
- **BeautifulSoup**: HTML 解析
- **Docker**: 容器化部署

### 核心模块说明

- **gttc_scraper.py**: 处理 GTTC 网站的数据抓取和解析
- **feishu_uploader_utils.py**: 封装飞书 API 调用
- **gttc_to_feishu_uploader.py**: 主业务逻辑，协调数据抓取和上传
- **scheduler.py**: 定时任务调度器

## 📄 许可证

本项目仅供学习和内部使用，请遵守相关网站的使用条款。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

---

**注意**: 请确保在使用前正确配置所有必要的环境变量和权限设置。 