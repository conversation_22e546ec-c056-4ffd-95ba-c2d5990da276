#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进后系统测试脚本
"""

import sys
from datetime import datetime

from yuntu_feishu_sync import SyncManager
from yuntu_feishu_sync.utils import setup_logger

def test_table_schema_match():
    """测试表格结构匹配"""
    print("📋 测试表格结构匹配...")
    try:
        sync_manager = SyncManager()
        
        # 获取表格结构
        schema = sync_manager.get_table_schema()
        fields = schema.get('fields', [])
        
        # 检查字段映射
        from yuntu_feishu_sync.config import FIELD_MAPPING
        existing_field_names = {field['field_name'] for field in fields}
        mapped_field_names = set(FIELD_MAPPING.values())
        
        missing_fields = mapped_field_names - existing_field_names
        
        if missing_fields:
            print(f"❌ 配置中的字段在表格中不存在: {missing_fields}")
            return False
        else:
            print("✅ 所有配置字段都在表格中存在")
            print(f"   映射字段数量: {len(FIELD_MAPPING)}")
            print(f"   表格字段数量: {len(fields)}")
            return True
            
    except Exception as e:
        print(f"❌ 表格结构测试失败: {e}")
        return False

def test_data_processing():
    """测试数据处理"""
    print("\n🔄 测试数据处理...")
    try:
        from yuntu_feishu_sync.core import DataProcessor
        
        # 模拟云图数据
        mock_yuntu_data = {
            'distribution_list': [
                {
                    'trigger_point': {
                        'trigger_point_id': '600104',
                        'trigger_point_name': '通投-品牌广告-搜索品专'
                    },
                    'basic_index': {
                        'convert_uv': '11329',
                        'convert_rate': 0.015308004176327801,
                        'convert_new_rate': 0.8123038156701443,
                        'average_transaction_value': 552.5076150199264
                    },
                    'bm_basic_index': {
                        'convert_uv': '382',
                        'convert_rate': 0.0037518295867372447,
                        'convert_new_rate': 0.8014416701110564,
                        'average_transaction_value': 1402.7655020712064
                    }
                }
            ]
        }
        
        data_processor = DataProcessor()
        records = data_processor.process_yuntu_data(mock_yuntu_data, "2025/06")
        
        if records:
            print(f"✅ 数据处理成功: {len(records)} 条记录")
            
            # 显示第一条记录
            first_record = records[0]
            print("   第一条记录:")
            for key, value in first_record.items():
                print(f"      {key}: {value}")
            
            # 测试验证
            valid_records = data_processor.validate_records(records)
            print(f"   验证后记录: {len(valid_records)} 条")
            
            # 测试去重
            unique_records = data_processor.deduplicate_records(valid_records)
            print(f"   去重后记录: {len(unique_records)} 条")
            
            return True
        else:
            print("❌ 数据处理失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据处理测试失败: {e}")
        return False

def test_month_data_request():
    """测试月份数据请求"""
    print("\n🌐 测试月份数据请求...")
    try:
        from yuntu_feishu_sync.core import YuntuClient
        
        yuntu_client = YuntuClient()
        
        # 测试获取当前月数据
        current_date = datetime.now()
        print(f"   测试获取 {current_date.year}年{current_date.month}月 的数据...")
        
        data = yuntu_client.get_month_data(current_date.year, current_date.month)
        
        if data:
            distribution_list = data.get('distribution_list', [])
            print(f"✅ 成功获取月份数据: {len(distribution_list)} 个触点")
            
            if distribution_list:
                first_item = distribution_list[0]
                trigger_point = first_item.get('trigger_point', {})
                print(f"   第一个触点: {trigger_point.get('trigger_point_name', '未知')}")
            
            return True
        else:
            print("❌ 获取月份数据失败")
            return False
            
    except Exception as e:
        print(f"❌ 月份数据请求测试失败: {e}")
        return False

def test_latest_data_sync():
    """测试最新数据同步"""
    print("\n🔄 测试最新数据同步...")
    try:
        sync_manager = SyncManager()
        
        # 测试获取最新数据
        result = sync_manager.sync_latest_data()
        
        if result.get('success'):
            print("✅ 最新数据同步测试成功")
            print(f"   数据周期: {result.get('data_period', '未知')}")
            print(f"   总记录数: {result.get('total_records', 0)}")
            print(f"   新增记录: {result.get('added', 0)}")
            print(f"   更新记录: {result.get('updated', 0)}")
            print(f"   跳过记录: {result.get('skipped', 0)}")
            print(f"   失败记录: {result.get('failed', 0)}")
            return True
        else:
            print(f"❌ 最新数据同步失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 最新数据同步测试失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logger()
    
    print("🧪 改进后云图飞书同步工具 - 系统测试")
    print("=" * 60)
    
    # 执行测试
    tests = [
        ("表格结构匹配", test_table_schema_match),
        ("数据处理", test_data_processing),
        ("月份数据请求", test_month_data_request),
        ("最新数据同步", test_latest_data_sync),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试出现异常: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n📊 测试结果汇总:")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name:<15}: {status}")
        if success:
            passed += 1
    
    print("=" * 40)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！改进后的系统可以正常运行")
        print("\n💡 接下来可以:")
        print("   1. 运行 python main.py --latest 同步最新数据")
        print("   2. 运行 python main.py --historical 同步历史数据")
        print("   3. 运行 python main.py --test 进行完整测试")
        return 0
    else:
        print(f"\n⚠️  {total - passed} 项测试失败，请检查配置和环境")
        failed_tests = [name for name, success in results.items() if not success]
        print(f"失败的测试: {', '.join(failed_tests)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
