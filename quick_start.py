#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动脚本 - 一键完成表格结构读取和数据同步
"""

import sys
from pathlib import Path

from yuntu_feishu_sync import SyncManager
from yuntu_feishu_sync.utils import setup_logger

def main():
    """主函数"""
    logger = setup_logger()
    
    print("⚡ 云图飞书同步工具 - 快速启动")
    print("=" * 50)
    
    try:
        # 初始化同步管理器
        sync_manager = SyncManager()
        
        # 步骤1: 测试连接
        print("🔗 步骤1: 测试所有连接...")
        results = sync_manager.test_all_connections()
        
        failed_connections = [service for service, success in results.items() if not success]
        
        if failed_connections:
            print(f"❌ 连接测试失败: {', '.join(failed_connections)}")
            print("请检查配置后重试")
            return 1
        
        print("✅ 所有连接测试通过")
        
        # 步骤2: 读取表格结构
        print("\n📋 步骤2: 读取表格结构...")
        schema_file = sync_manager.save_table_schema()
        print(f"✅ 表格结构已保存到: {schema_file}")
        
        # 显示表格信息
        schema = sync_manager.get_table_schema()
        fields = schema.get('fields', [])
        print(f"📊 表格字段数量: {len(fields)}")
        
        # 检查字段匹配
        from yuntu_feishu_sync.config import FIELD_MAPPING
        existing_field_names = {field['field_name'] for field in fields}
        mapped_field_names = set(FIELD_MAPPING.values())
        missing_fields = mapped_field_names - existing_field_names
        
        if missing_fields:
            print(f"⚠️  配置中的字段在表格中不存在: {', '.join(missing_fields)}")
            print("建议检查字段映射配置")
        else:
            print("✅ 所有配置字段都在表格中存在")
        
        # 步骤3: 数据同步
        print("\n🔄 步骤3: 开始数据同步...")
        print("首次运行建议同步历史数据，后续可以只同步最新数据")

        # 先尝试同步最新数据
        result = sync_manager.sync_latest_data()
        
        if result.get('success'):
            print("✅ 数据同步成功！")
            print(f"📊 同步统计:")
            print(f"   总记录数: {result.get('total_records', 0)}")
            print(f"   新增记录: {result.get('added', 0)}")
            print(f"   更新记录: {result.get('updated', 0)}")
            print(f"   失败记录: {result.get('failed', 0)}")
            
            if result.get('failed', 0) > 0:
                print("⚠️  部分记录同步失败，请检查日志")
                return 1
            
            print("\n🎉 快速启动完成！")
            return 0
        else:
            print(f"❌ 数据同步失败: {result.get('error', '未知错误')}")
            return 1
    
    except KeyboardInterrupt:
        print("\n⏹️  用户中断操作")
        return 1
    except Exception as e:
        logger.error(f"快速启动过程中出错: {e}")
        print(f"❌ 快速启动失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
