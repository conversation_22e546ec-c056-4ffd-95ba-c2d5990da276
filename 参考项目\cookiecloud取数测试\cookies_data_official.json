{"github": [{"domain": ".github.com", "expirationDate": 1767412896.317548, "hostOnly": false, "httpOnly": false, "name": "_octo", "path": "/", "sameSite": "lax", "secure": true, "session": false, "storeId": "0", "value": "GH1.1.1234544226.1735876896"}, {"domain": ".github.com", "hostOnly": false, "httpOnly": false, "name": "cpu_bucket", "path": "/", "sameSite": "lax", "secure": true, "session": true, "storeId": "0", "value": "xlg"}, {"domain": ".github.com", "hostOnly": false, "httpOnly": false, "name": "preferred_color_mode", "path": "/", "sameSite": "lax", "secure": true, "session": true, "storeId": "0", "value": "light"}, {"domain": ".github.com", "hostOnly": false, "httpOnly": false, "name": "tz", "path": "/", "sameSite": "lax", "secure": true, "session": true, "storeId": "0", "value": "Asia%2FShanghai"}, {"domain": "github.com", "expirationDate": 1780470238.080999, "hostOnly": true, "httpOnly": true, "name": "_device_id", "path": "/", "sameSite": "lax", "secure": true, "session": false, "storeId": "0", "value": "80eba56ad4bc7ac05a19166d0d17592a"}, {"domain": "github.com", "hostOnly": true, "httpOnly": true, "name": "tz", "path": "/", "sameSite": "lax", "secure": true, "session": true, "storeId": "0", "value": "Asia%2FShanghai"}, {"domain": ".github.com", "hostOnly": false, "httpOnly": false, "name": "color_mode", "path": "/", "sameSite": "lax", "secure": true, "session": true, "storeId": "0", "value": "%7B%22color_mode%22%3A%22auto%22%2C%22light_theme%22%3A%7B%22name%22%3A%22light%22%2C%22color_mode%22%3A%22light%22%7D%2C%22dark_theme%22%3A%7B%22name%22%3A%22dark%22%2C%22color_mode%22%3A%22dark%22%7D%7D"}, {"domain": "github.com", "hostOnly": true, "httpOnly": false, "name": "trackingAllowed", "path": "/", "sameSite": "unspecified", "secure": false, "session": true, "storeId": "0", "value": "false"}, {"domain": "docs.github.com", "expirationDate": 1770286128, "hostOnly": true, "httpOnly": false, "name": "_docs-events", "path": "/", "sameSite": "strict", "secure": true, "session": false, "storeId": "0", "value": "f99d8248-f6c6-401c-8b2e-918a6dbf8706"}, {"domain": ".github.com", "expirationDate": 1754302175, "hostOnly": false, "httpOnly": false, "name": "GHCC", "path": "/", "sameSite": "lax", "secure": true, "session": false, "storeId": "0", "value": "Required:1-Analytics:1-SocialMedia:1-Advertising:1"}, {"domain": "docs.github.com", "expirationDate": 1770286291, "hostOnly": true, "httpOnly": false, "name": "toolPreferred", "path": "/", "sameSite": "strict", "secure": true, "session": false, "storeId": "0", "value": "windowsterminal"}, {"domain": "github.com", "hostOnly": true, "httpOnly": false, "name": "loginbox_strategy", "path": "/", "sameSite": "unspecified", "secure": false, "session": true, "storeId": "0", "value": "1"}, {"domain": "github.com", "hostOnly": true, "httpOnly": false, "name": "popShowed10s", "path": "/", "sameSite": "unspecified", "secure": false, "session": true, "storeId": "0", "value": "yes"}, {"domain": "github.com", "hostOnly": true, "httpOnly": false, "name": "unlogin_scroll_step", "path": "/", "sameSite": "unspecified", "secure": false, "session": true, "storeId": "0", "value": "1"}, {"domain": "github.com", "hostOnly": true, "httpOnly": false, "name": "anthropic-consent-preferences", "path": "/", "sameSite": "unspecified", "secure": false, "session": true, "storeId": "0", "value": "%7B%22analytics%22%3Afalse%2C%22marketing%22%3Afalse%7D"}, {"domain": "github.com", "hostOnly": true, "httpOnly": false, "name": "consent", "path": "/", "sameSite": "unspecified", "secure": false, "session": true, "storeId": "0", "value": "rejected"}, {"domain": "github.com", "expirationDate": 1755577518.154961, "hostOnly": true, "httpOnly": true, "name": "saved_user_sessions", "path": "/", "sameSite": "lax", "secure": true, "session": false, "storeId": "0", "value": "149945786%3A8RT5c2Skn2Mu2p-tCvcqTdAfsohjh4nG9M1hwGR1goX3MbT8"}, {"domain": "github.com", "expirationDate": 1750143838.081079, "hostOnly": true, "httpOnly": true, "name": "user_session", "path": "/", "sameSite": "lax", "secure": true, "session": false, "storeId": "0", "value": "8RT5c2Skn2Mu2p-tCvcqTdAfsohjh4nG9M1hwGR1goX3MbT8"}, {"domain": "github.com", "expirationDate": 1750143838.081107, "hostOnly": true, "httpOnly": true, "name": "__Host-user_session_same_site", "path": "/", "sameSite": "strict", "secure": true, "session": false, "storeId": "0", "value": "8RT5c2Skn2Mu2p-tCvcqTdAfsohjh4nG9M1hwGR1goX3MbT8"}, {"domain": ".github.com", "expirationDate": 1779337518.155076, "hostOnly": false, "httpOnly": true, "name": "logged_in", "path": "/", "sameSite": "lax", "secure": true, "session": false, "storeId": "0", "value": "yes"}, {"domain": ".github.com", "expirationDate": 1779337518.155095, "hostOnly": false, "httpOnly": true, "name": "dotcom_user", "path": "/", "sameSite": "lax", "secure": true, "session": false, "storeId": "0", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"domain": "github.com", "hostOnly": true, "httpOnly": true, "name": "_gh_sess", "path": "/", "sameSite": "lax", "secure": true, "session": true, "storeId": "0", "value": "Bwa9vrzwpbvKSto1Oyp%2BL%2F6%2BXtdSGKutz%2BQHL7rVOXpwTPOD11rprbxm%2FePqq09XIVWGwzpI3usryHQNbpO7gyUn%2F4V5dYgKOMgNtHjXRJifycroVE%2F0qE2VyJTMUrbSQEj4ufoPvOe5uwTSIrzVXLc1HLopoXBmtSfnpfQe4S4DS0Er69zE2flQjq7MMuABU2CR0W%2B03k%2FiDc4D%2FEnGVJQSJMK7MqTsbqc%2FFkbdHv1h42MF7tDU69QwbW9IfAFNtbamBJoR1n4QRd1PZnhmlZNUNrbI%2BSmgYZcCDdJ9yTAZOTZAPWCJBI2mnIdb2zuNoKGlznXo459okAVubgzeu8R3knfM3DawfmPI2XXMYyeqpn5Q5IRuZRT5ywMBrWrxpEPjvu6SzBTKw54X8uTplODDX%2BOysbQA%2F2whJfDtwg4RD2pdxd%2BKVsrbC%2Bpzm4jxHyjaUYpMH7l2TkGQ%2FIBOjmeFE4sDJBxUor0qOHMbuz8jEB7Aco41gHNC3s27pydtIZMqF0tem022%2BA7Ka6TpHiIYrdxKtDUEcEJiU1nzqeoKBit%2BvaK0ovAzNLE%3D--JloRi948Nv1k4RqP--qDP3gMrX2VO9oxE9UujD6Q%3D%3D"}]}