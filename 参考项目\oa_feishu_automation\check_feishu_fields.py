#!/usr/bin/env python3
import json

# 从上面的输出复制飞书payment表的字段信息
payment_fields = [
    {'field_id': 'fld3DDpgSH', 'field_name': '流程编号', 'field_type': 1},
    {'field_id': 'fldRXN1v9d', 'field_name': '合同规定付款条件', 'field_type': 1},
    {'field_id': 'fld5zTdLht', 'field_name': ' 税额', 'field_type': 1},
    {'field_id': 'fld2y7XhCd', 'field_name': '去税金额', 'field_type': 1},
    {'field_id': 'fld7BqKfvf', 'field_name': '附件', 'field_type': 1},
    {'field_id': 'fld60ianDx', 'field_name': '创建日期', 'field_type': 1},
    {'field_id': 'fld2XbeDyp', 'field_name': '当前节点', 'field_type': 3},
    {'field_id': 'fldSe3jLd4', 'field_name': '签字意见', 'field_type': 1},
    {'field_id': 'fld3GM1DqJ', 'field_name': '费用类型', 'field_type': 1},
    {'field_id': 'fld4zzXyTP', 'field_name': '供应商名称', 'field_type': 1},
    {'field_id': 'fld4slbVwV', 'field_name': '是否有合同流程', 'field_type': 3},
    {'field_id': 'fld3c7TM5x', 'field_name': '企业名称', 'field_type': 3},
    {'field_id': 'fld2m0h7tO', 'field_name': '款项用途', 'field_type': 1},
    {'field_id': 'fld3axiutR', 'field_name': '本次支付金额', 'field_type': 1},
    {'field_id': 'fld5DSb6lZ', 'field_name': '专 票税额（不是专票填写0）', 'field_type': 1},
    {'field_id': 'fld1YvM7EP', 'field_name': '去税总金额', 'field_type': 1},
    {'field_id': 'fld6yO3YJq', 'field_name': ' 创建人', 'field_type': 1},
    {'field_id': 'fld18UW1Sw', 'field_name': '费用所属部 门', 'field_type': 1},
    {'field_id': 'fld1xLz5nR', 'field_name': '相关合同', 'field_type': 1},
    {'field_id': 'fld7ywxrZE', 'field_name': '创建人所属部门', 'field_type': 1},
    {'field_id': 'fld3yLOWVz', 'field_name': '金额', 'field_type': 1},
    {'field_id': 'fld6FWj5EV', 'field_name': '选择发票', 'field_type': 1},
    {'field_id': 'fld2N4T8xf', 'field_name': '发票类型', 'field_type': 3},
    {'field_id': 'fld1UjT7gz', 'field_name': '税率', 'field_type': 1}
]

print("=== 飞书 Payment 表字段详情 ===")
for i, field in enumerate(payment_fields, 1):
    field_name = field['field_name']
    print(f"{i:2d}. 字段名: '{field_name}' (长度: {len(field_name)})")
    if field_name.startswith(' ') or field_name.endswith(' '):
        print(f"    ⚠️  包含前导/后置空格!")
    
print("\n=== Excel列名（从之前的输出） ===")
excel_columns = [
    '流程编号', '企业名称', '发票类型', '金额', '税额', '税率', '合同规定付款条件', '去税金额', 
    '预计提供发票时间', '创建人', '创建日期', '当前节点', '本次支付金额', '附件', '签字意见', 
    '费用类型', '供应商名称', '是否有合同流程', '汇款币种', '专票税额（不是专票填写0）', 
    '去税总金额', '款项用途', '费用所属部门', '相关合同', '创建人所属部门', '选择发票'
]

for i, col in enumerate(excel_columns, 1):
    print(f"{i:2d}. '{col}'")

print("\n=== 匹配分析 ===")
feishu_names = [f['field_name'] for f in payment_fields]
for excel_col in excel_columns:
    # 寻找完全匹配
    exact_match = excel_col in feishu_names
    # 寻找去除空格后匹配
    stripped_matches = [f for f in feishu_names if f.strip() == excel_col]
    
    if exact_match:
        print(f"✅ '{excel_col}' -> 完全匹配")
    elif stripped_matches:
        print(f"🔧 '{excel_col}' -> 空格匹配: '{stripped_matches[0]}'")
    else:
        print(f"❌ '{excel_col}' -> 无匹配") 