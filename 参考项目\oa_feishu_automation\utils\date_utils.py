#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Date utilities for OA to Feishu automation
"""

from datetime import datetime, timedelta
from typing import <PERSON>ple

def get_date_range(days: int = 30) -> Tuple[str, str]:
    """
    Get date range for the last N days
    
    Args:
        days: Number of days to look back
        
    Returns:
        Tuple of (start_date, end_date) in YYYY-MM-DD format
    """
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    
    return (
        start_date.strftime('%Y-%m-%d'),
        end_date.strftime('%Y-%m-%d')
    )

def format_date_for_oa(date_str: str) -> str:
    """
    Format date string for OA API
    
    Args:
        date_str: Date string in YYYY-MM-DD format
        
    Returns:
        Formatted date string
    """
    return date_str

def parse_excel_date(date_value) -> str:
    """
    Parse date from Excel cell value
    
    Args:
        date_value: Date value from Excel cell
        
    Returns:
        Formatted date string
    """
    if isinstance(date_value, datetime):
        return date_value.strftime('%Y-%m-%d')
    elif isinstance(date_value, str):
        try:
            # Try to parse various date formats
            for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%m/%d/%Y', '%d/%m/%Y']:
                try:
                    parsed_date = datetime.strptime(date_value, fmt)
                    return parsed_date.strftime('%Y-%m-%d')
                except ValueError:
                    continue
            return date_value  # Return as-is if parsing fails
        except:
            return str(date_value)
    else:
        return str(date_value)

def get_current_timestamp() -> int:
    """
    Get current timestamp in milliseconds
    
    Returns:
        Current timestamp in milliseconds
    """
    return int(datetime.now().timestamp() * 1000)
